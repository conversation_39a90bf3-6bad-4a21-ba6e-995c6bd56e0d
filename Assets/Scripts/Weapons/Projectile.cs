using UnityEngine;

public class Projectile : MonoBehaviour
{
    [Header("Base Stats")]
    public float speed = 15f;
    public float damage = 10f;
    public float lifetime = 3f;      // Seconds before the projectile is destroyed if it hits nothing
    public int pierceCount = 0;      // How many enemies it can pass through (0 = destroyed on first hit)
    
    [Header("Homing (Optional)")]
    public bool isHoming = false;
    public float homingStrength = 8f;    // How quickly it turns towards the target
    public float homingActivationDelay = 0.1f; // Delay in seconds before homing starts
    public float homingSearchRadius = 15f; // Range to look for targets for homing

    private Rigidbody2D rb;
    private Transform currentTarget;    // For homing projectiles
    private int hitCount = 0;
    private float currentLifetime;
    private float timeSinceSpawned = 0f;

    // public PlayerController owner; // Could be used for advanced effects or ignoring owner collision

    void Awake()
    {
        rb = GetComponent<Rigidbody2D>();
        if (rb == null)
        {
            Debug.LogError("Projectile " + gameObject.name + " needs a Rigidbody2D component!");
            // For reliable trigger events, a Rigidbody2D is often needed even if kinematic.
            // gameObject.AddComponent<Rigidbody2D>().isKinematic = true; 
        }
        
        Collider2D col = GetComponent<Collider2D>();
        if (col == null)
        {
            Debug.LogError("Projectile " + gameObject.name + " needs a Collider2D component for hit detection!", this);
        }
        else if (!col.isTrigger)
        {
            Debug.LogWarning("Projectile's Collider2D (" + gameObject.name + ") should be set to 'Is Trigger = true' for OnTriggerEnter2D hit detection.", this);
        }
    }

    public void Initialize(Vector2 direction, float projDamage, int projPierce, float projSpeed, float projLifetime/*, PlayerController projOwner*/)
    {
        this.damage = projDamage;
        this.pierceCount = projPierce;
        this.speed = projSpeed;
        this.lifetime = projLifetime;
        // this.owner = projOwner;

        currentLifetime = this.lifetime;
        hitCount = 0;
        timeSinceSpawned = 0f;

        transform.up = direction.normalized; // Point the projectile (assuming its 'up' is its forward)

        if (isHoming)
        {
            // Delay target acquisition slightly or do it after homingActivationDelay
        }
    }

    void Update()
    {
        timeSinceSpawned += Time.deltaTime;
        currentLifetime -= Time.deltaTime;
        if (currentLifetime <= 0f)
        {
            DestroyProjectile();
            return;
        }

        Move();
    }

    void Move()
    {
        if (isHoming && timeSinceSpawned >= homingActivationDelay)
        {
            if (currentTarget == null || !currentTarget.gameObject.activeInHierarchy)
            {
                FindNewTargetForHoming();
            }

            if (currentTarget != null)
            {
                Vector2 directionToTarget = (currentTarget.position - transform.position).normalized;
                // Rotate towards target
                float angle = Mathf.Atan2(directionToTarget.y, directionToTarget.x) * Mathf.Rad2Deg - 90f; // Sprite Y axis is forward
                Quaternion targetRotation = Quaternion.AngleAxis(angle, Vector3.forward);
                transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, homingStrength * Time.deltaTime);
            }
        }
        // Always move forward based on current rotation
        transform.position += transform.up * speed * Time.deltaTime;
    }

    void FindNewTargetForHoming()
    {
        GameObject[] enemies = GameObject.FindGameObjectsWithTag("Enemy");
        float closestDistSqr = homingSearchRadius * homingSearchRadius;
        Transform newTarget = null;

        foreach (var enemyGO in enemies)
        {
            if (!enemyGO.activeInHierarchy) continue;
            Vector2 diff = enemyGO.transform.position - transform.position;
            if (diff.sqrMagnitude < closestDistSqr)
            {
                closestDistSqr = diff.sqrMagnitude;
                newTarget = enemyGO.transform;
            }
        }
        currentTarget = newTarget;
    }

    void OnTriggerEnter2D(Collider2D other)
    {
        if (other.CompareTag("Enemy"))
        {
            // Enemy enemyComponent = other.GetComponent<Enemy>(); // Temporarily comment out
            // if (enemyComponent != null)
            // {
            //     // enemyComponent.TakeDamage(this.damage); // Temporarily comment out
            // }
            Debug.Log($"Projectile hit {other.name} (Enemy script not yet implemented, direct damage not applied)."); // Modified log

            hitCount++;
            if (hitCount > pierceCount) 
            {
                DestroyProjectile();
            }
        }
        // Example: Destroy if it hits a wall/obstacle
        // else if (other.CompareTag("Obstacle") || other.gameObject.layer == LayerMask.NameToLayer("Environment"))
        // {
        //     DestroyProjectile();
        // }
    }

    void DestroyProjectile()
    {
        // Optional: Instantiate an impact effect prefab here
        // if (impactEffectPrefab != null) Instantiate(impactEffectPrefab, transform.position, Quaternion.identity);
        Destroy(gameObject);
    }
}