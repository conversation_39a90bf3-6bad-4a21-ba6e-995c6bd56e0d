using UnityEngine;

public abstract class Enemy : MonoBehaviour
{
    [<PERSON><PERSON>("Enemy Stats")]
    public string enemyName = "Basic Enemy";
    public int maxHealth = 50;
    public int currentHealth;
    public float moveSpeed = 2f;
    public int damage = 10;
    public int experienceValue = 10; // XP dropped when killed

    [<PERSON><PERSON>("Combat Settings")]
    public float attackRange = 1f; // Range to attack player
    public float attackCooldown = 1f; // Time between attacks
    
    protected Transform playerTransform;
    protected PlayerController playerController;
    protected float currentAttackCooldown = 0f;
    protected bool isDead = false;

    // Events for when enemy dies (useful for spawner management)
    public static event System.Action<Enemy> OnEnemyDeath;

    protected virtual void Start()
    {
        currentHealth = maxHealth;
        
        // Find the player
        GameObject playerObject = GameObject.FindGameObjectWithTag("Player");
        if (playerObject != null)
        {
            playerTransform = playerObject.transform;
            playerController = playerObject.GetComponent<PlayerController>();
        }
        else
        {
            Debug.LogWarning($"Enemy '{enemyName}' could not find Player. Make sure Player has 'Player' tag.");
        }

        // Ensure enemy has required components
        Collider2D col = GetComponent<Collider2D>();
        if (col == null)
        {
            Debug.LogError($"Enemy '{enemyName}' on {gameObject.name} requires a Collider2D component.", this);
        }

        Rigidbody2D rb = GetComponent<Rigidbody2D>();
        if (rb == null)
        {
            Debug.LogWarning($"Enemy '{enemyName}' on {gameObject.name} should have a Rigidbody2D for better physics.", this);
        }
    }

    protected virtual void Update()
    {
        if (isDead || playerTransform == null) return;

        // Update attack cooldown
        if (currentAttackCooldown > 0)
        {
            currentAttackCooldown -= Time.deltaTime;
        }

        // Calculate distance to player
        float distanceToPlayer = Vector2.Distance(transform.position, playerTransform.position);

        // Attack if in range and cooldown is ready
        if (distanceToPlayer <= attackRange && currentAttackCooldown <= 0f)
        {
            AttackPlayer();
            currentAttackCooldown = attackCooldown;
        }
        else if (distanceToPlayer > attackRange)
        {
            // Move towards player if not in attack range
            MoveTowardsPlayer();
        }
    }

    protected virtual void MoveTowardsPlayer()
    {
        if (playerTransform == null) return;

        Vector2 direction = (playerTransform.position - transform.position).normalized;
        transform.position = Vector2.MoveTowards(transform.position, playerTransform.position, moveSpeed * Time.deltaTime);
        
        // Optional: Rotate enemy to face movement direction
        if (direction.x != 0)
        {
            transform.localScale = new Vector3(direction.x > 0 ? 1 : -1, 1, 1);
        }
    }

    protected virtual void AttackPlayer()
    {
        if (playerController != null)
        {
            playerController.TakeDamage(damage);
            Debug.Log($"{enemyName} attacked player for {damage} damage!");
            
            // Optional: Add attack animation or effect here
            OnAttackPerformed();
        }
    }

    protected virtual void OnAttackPerformed()
    {
        // Override in derived classes for specific attack effects
        // e.g., play attack animation, spawn attack effect, etc.
    }

    public virtual void TakeDamage(float damageAmount)
    {
        if (isDead) return;

        currentHealth -= Mathf.RoundToInt(damageAmount);
        Debug.Log($"{enemyName} took {damageAmount} damage. Health: {currentHealth}/{maxHealth}");

        // Optional: Add damage effect here (flash red, damage number popup, etc.)
        OnDamageReceived(damageAmount);

        if (currentHealth <= 0)
        {
            Die();
        }
    }

    protected virtual void OnDamageReceived(float damageAmount)
    {
        // Override in derived classes for damage effects
        // e.g., flash sprite, play hurt sound, etc.
    }

    protected virtual void Die()
    {
        if (isDead) return;
        
        isDead = true;
        Debug.Log($"{enemyName} has died!");

        // Drop experience gem
        DropExperienceGem();

        // Notify other systems that this enemy died
        OnEnemyDeath?.Invoke(this);

        // Optional: Add death effect here
        OnDeathEffect();

        // Destroy the enemy object
        Destroy(gameObject);
    }

    protected virtual void DropExperienceGem()
    {
        // Try to find the experience gem prefab
        // For now, we'll create a simple placeholder
        // In a real implementation, you'd have a prefab reference
        
        GameObject expGemPrefab = Resources.Load<GameObject>("Prefabs/ExperienceGem");
        if (expGemPrefab != null)
        {
            GameObject expGem = Instantiate(expGemPrefab, transform.position, Quaternion.identity);
            ExperienceGem gemScript = expGem.GetComponent<ExperienceGem>();
            if (gemScript != null)
            {
                gemScript.experienceValue = experienceValue;
            }
        }
        else
        {
            Debug.LogWarning($"Experience gem prefab not found in Resources/Prefabs/ExperienceGem. Enemy {enemyName} could not drop XP gem.");
        }
    }

    protected virtual void OnDeathEffect()
    {
        // Override in derived classes for death effects
        // e.g., play death animation, spawn particles, play sound, etc.
    }

    // Optional: Draw attack range in Scene view for debugging
    protected virtual void OnDrawGizmosSelected()
    {
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(transform.position, attackRange);
    }

    // Collision detection for projectiles
    protected virtual void OnTriggerEnter2D(Collider2D other)
    {
        // This will be called when projectiles hit the enemy
        // The actual damage is handled by the projectile script
        // This is just for additional effects if needed
    }
}
