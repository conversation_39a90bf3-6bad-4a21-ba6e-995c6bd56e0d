using UnityEngine;

public class NoTagGameSetup : MonoBehaviour
{
    [Header("No-Tag Game Setup")]
    public bool setupOnStart = true;
    public bool clearExistingFirst = true;
    
    void Start()
    {
        if (setupOnStart)
        {
            SetupGameWithoutTags();
        }
    }
    
    [ContextMenu("Setup Game Without Tags")]
    public void SetupGameWithoutTags()
    {
        Debug.Log("=== NO-TAG GAME SETUP STARTING ===");
        
        if (clearExistingFirst)
        {
            ClearExisting();
        }
        
        CreatePlayer();
        CreateGameManager();
        CreateEnemySpawner();
        
        // Create immediate test enemies
        Invoke("CreateTestEnemies", 0.5f);
        
        Debug.Log("=== NO-TAG GAME SETUP COMPLETE ===");
        Debug.Log("This setup doesn't rely on Unity tags!");
    }
    
    void ClearExisting()
    {
        // Clear by component type instead of tags
        PlayerController[] players = FindObjectsOfType<PlayerController>();
        foreach (PlayerController player in players)
        {
            DestroyImmediate(player.gameObject);
        }
        
        BasicEnemy[] enemies = FindObjectsOfType<BasicEnemy>();
        foreach (BasicEnemy enemy in enemies)
        {
            DestroyImmediate(enemy.gameObject);
        }
        
        GameManager[] managers = FindObjectsOfType<GameManager>();
        foreach (GameManager manager in managers)
        {
            DestroyImmediate(manager.gameObject);
        }
        
        SimpleEnemySpawner[] spawners = FindObjectsOfType<SimpleEnemySpawner>();
        foreach (SimpleEnemySpawner spawner in spawners)
        {
            DestroyImmediate(spawner.gameObject);
        }
    }
    
    void CreatePlayer()
    {
        Debug.Log("Creating player (no tags)...");
        
        GameObject player = new GameObject("Player");
        player.transform.position = Vector3.zero;
        
        // Don't set tag - we'll use components for identification
        
        // Add PlayerController
        PlayerController pc = player.AddComponent<PlayerController>();
        pc.moveSpeed = 5f;
        pc.maxHealth = 100;
        pc.currentHealth = 100;
        
        // Add PlayerLevel
        PlayerLevel pl = player.AddComponent<PlayerLevel>();
        pl.currentLevel = 1;
        pl.currentExperience = 0;
        pl.experienceToNextLevel = 100;
        
        // Add physics
        Rigidbody2D rb = player.AddComponent<Rigidbody2D>();
        rb.gravityScale = 0f;
        rb.freezeRotation = true;
        
        // Add collider
        CircleCollider2D col = player.AddComponent<CircleCollider2D>();
        col.radius = 0.5f;
        
        // Add visual
        SpriteRenderer sr = player.AddComponent<SpriteRenderer>();
        sr.sprite = CreateCircleSprite(Color.blue, 64);
        sr.sortingOrder = 2;
        
        // Create weapon
        GameObject weaponObj = new GameObject("MagicWand");
        weaponObj.transform.SetParent(player.transform);
        
        MagicWand weapon = weaponObj.AddComponent<MagicWand>();
        weapon.baseDamage = 25f;
        weapon.baseCooldown = 1f;
        weapon.projectileSpeed = 15f;
        weapon.findTargetRange = 15f;
        
        // Create projectile prefab for the weapon
        weapon.projectilePrefab = CreateProjectilePrefab();
        
        Debug.Log("Player created (component-based identification)!");
    }
    
    GameObject CreateProjectilePrefab()
    {
        GameObject projectile = new GameObject("MagicProjectile");
        
        // Add Projectile script
        Projectile proj = projectile.AddComponent<Projectile>();
        proj.speed = 15f;
        proj.damage = 25f;
        proj.lifetime = 3f;
        proj.pierceCount = 0;
        
        // Add physics
        Rigidbody2D rb = projectile.AddComponent<Rigidbody2D>();
        rb.gravityScale = 0f;
        
        // Add trigger collider
        CircleCollider2D col = projectile.AddComponent<CircleCollider2D>();
        col.radius = 0.1f;
        col.isTrigger = true;
        
        // Add visual
        SpriteRenderer sr = projectile.AddComponent<SpriteRenderer>();
        sr.sprite = CreateCircleSprite(Color.yellow, 16);
        sr.sortingOrder = 3;
        
        return projectile;
    }
    
    void CreateGameManager()
    {
        Debug.Log("Creating GameManager...");
        
        GameObject gm = new GameObject("GameManager");
        GameManager gmScript = gm.AddComponent<GameManager>();
        gmScript.gameDurationMinutes = 30f;
        gmScript.startGameAutomatically = true;
        
        Debug.Log("GameManager created!");
    }
    
    void CreateEnemySpawner()
    {
        Debug.Log("Creating component-based enemy spawner...");
        
        GameObject spawnerObj = new GameObject("ComponentEnemySpawner");
        ComponentEnemySpawner spawner = spawnerObj.AddComponent<ComponentEnemySpawner>();
        
        // Find player by component
        PlayerController player = FindObjectOfType<PlayerController>();
        if (player != null)
        {
            spawner.playerTransform = player.transform;
        }
        
        spawner.spawnRadius = 15f;
        spawner.minSpawnDistance = 8f;
        spawner.spawnInterval = 2f;
        spawner.maxEnemies = 25;
        
        Debug.Log("Component-based enemy spawner created!");
    }
    
    void CreateTestEnemies()
    {
        Debug.Log("Creating test enemies (no tags)...");
        
        PlayerController player = FindObjectOfType<PlayerController>();
        if (player == null)
        {
            Debug.LogError("No player found!");
            return;
        }
        
        // Create 3 test enemies
        for (int i = 0; i < 3; i++)
        {
            float angle = i * 120f * Mathf.Deg2Rad;
            Vector3 spawnPos = player.transform.position + new Vector3(
                Mathf.Cos(angle) * 6f,
                Mathf.Sin(angle) * 6f,
                0f
            );
            
            CreateEnemyAt(spawnPos, $"TestEnemy_{i}");
        }
        
        Debug.Log("Test enemies created!");
    }
    
    GameObject CreateEnemyAt(Vector3 position, string name)
    {
        GameObject enemy = new GameObject(name);
        enemy.transform.position = position;
        
        // Don't set tag - use components for identification
        
        // Add BasicEnemy script
        BasicEnemy enemyScript = enemy.AddComponent<BasicEnemy>();
        
        // Add physics
        Rigidbody2D rb = enemy.AddComponent<Rigidbody2D>();
        rb.gravityScale = 0f;
        rb.freezeRotation = true;
        
        // Add collider
        CircleCollider2D col = enemy.AddComponent<CircleCollider2D>();
        col.radius = 0.4f;
        
        // Add visual
        SpriteRenderer sr = enemy.AddComponent<SpriteRenderer>();
        sr.sprite = CreateCircleSprite(Color.red, 48);
        sr.sortingOrder = 1;
        
        return enemy;
    }
    
    Sprite CreateCircleSprite(Color color, int size)
    {
        Texture2D texture = new Texture2D(size, size);
        Color[] pixels = new Color[size * size];
        
        Vector2 center = new Vector2(size / 2f, size / 2f);
        float radius = size / 2f - 2f;
        
        for (int x = 0; x < size; x++)
        {
            for (int y = 0; y < size; y++)
            {
                float distance = Vector2.Distance(new Vector2(x, y), center);
                if (distance <= radius)
                {
                    pixels[y * size + x] = color;
                }
                else
                {
                    pixels[y * size + x] = Color.clear;
                }
            }
        }
        
        texture.SetPixels(pixels);
        texture.Apply();
        
        return Sprite.Create(texture, new Rect(0, 0, size, size), new Vector2(0.5f, 0.5f));
    }
    
    void Update()
    {
        if (Input.GetKeyDown(KeyCode.F7))
        {
            SetupGameWithoutTags();
        }
    }
    
    void OnGUI()
    {
        GUILayout.BeginArea(new Rect(10, 10, 300, 80));
        GUILayout.Label("=== NO-TAG SETUP ===");
        GUILayout.Label("F7 - Setup game without tags");
        if (GUILayout.Button("Setup Now (No Tags)"))
        {
            SetupGameWithoutTags();
        }
        GUILayout.EndArea();
    }
}
