using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
#endif

public class QuickSetup : MonoBehaviour
{
    [Head<PERSON>("Quick Setup for Testing")]
    [SerializeField] private bool autoSetupOnStart = true;
    
    void Start()
    {
        if (autoSetupOnStart)
        {
            SetupBasicGame();
        }
    }
    
    [ContextMenu("Setup Basic Game")]
    public void SetupBasicGame()
    {
        Debug.Log("QuickSetup: Setting up basic game for testing...");
        
        // Create basic player if not exists
        CreatePlayerIfNeeded();
        
        // Create basic enemy prefab if not exists
        CreateBasicEnemyPrefab();
        
        // Create basic projectile prefab if not exists
        CreateBasicProjectilePrefab();
        
        // Create experience gem prefab if not exists
        CreateExperienceGemPrefab();
        
        // Setup game manager
        SetupGameManager();
        
        // Setup enemy spawner
        SetupEnemySpawner();
        
        Debug.Log("QuickSetup: Basic game setup complete!");
    }
    
    void CreatePlayerIfNeeded()
    {
        GameObject player = GameObject.FindGameObjectWithTag("Player");
        if (player == null)
        {
            Debug.Log("QuickSetup: Creating player...");
            
            // Create player object
            player = new GameObject("Player");
            player.tag = "Player";
            
            // Add components
            player.AddComponent<PlayerController>();
            player.AddComponent<PlayerLevel>();
            
            Rigidbody2D rb = player.AddComponent<Rigidbody2D>();
            rb.gravityScale = 0f;
            rb.freezeRotation = true;
            
            CircleCollider2D col = player.AddComponent<CircleCollider2D>();
            col.radius = 0.5f;
            
            // Add sprite renderer with a simple colored square
            SpriteRenderer sr = player.AddComponent<SpriteRenderer>();
            sr.sprite = CreateSimpleSprite(Color.blue, "Player");
            
            // Position player at origin
            player.transform.position = Vector3.zero;
            
            // Create weapon as child
            GameObject weapon = new GameObject("MagicWand");
            weapon.transform.SetParent(player.transform);
            weapon.AddComponent<MagicWand>();
            
            Debug.Log("QuickSetup: Player created successfully!");
        }
        else
        {
            Debug.Log("QuickSetup: Player already exists.");
        }
    }
    
    void CreateBasicEnemyPrefab()
    {
        // Check if prefab already exists
        GameObject existingPrefab = Resources.Load<GameObject>("Prefabs/BasicEnemy");
        if (existingPrefab != null)
        {
            Debug.Log("QuickSetup: BasicEnemy prefab already exists.");
            return;
        }
        
        Debug.Log("QuickSetup: Creating BasicEnemy prefab...");
        
        // Create enemy object
        GameObject enemy = new GameObject("BasicEnemy");
        enemy.tag = "Enemy";
        
        // Add components
        enemy.AddComponent<BasicEnemy>();
        
        Rigidbody2D rb = enemy.AddComponent<Rigidbody2D>();
        rb.gravityScale = 0f;
        rb.freezeRotation = true;
        
        CircleCollider2D col = enemy.AddComponent<CircleCollider2D>();
        col.radius = 0.4f;
        
        // Add sprite renderer
        SpriteRenderer sr = enemy.AddComponent<SpriteRenderer>();
        sr.sprite = CreateSimpleSprite(Color.red, "Enemy");
        
        // Save as prefab
        SaveAsPrefab(enemy, "Assets/Resources/Prefabs/BasicEnemy.prefab");
        
        // Clean up the temporary object
        DestroyImmediate(enemy);
        
        Debug.Log("QuickSetup: BasicEnemy prefab created!");
    }
    
    void CreateBasicProjectilePrefab()
    {
        GameObject existingPrefab = Resources.Load<GameObject>("Prefabs/MagicProjectile");
        if (existingPrefab != null)
        {
            Debug.Log("QuickSetup: MagicProjectile prefab already exists.");
            return;
        }
        
        Debug.Log("QuickSetup: Creating MagicProjectile prefab...");
        
        GameObject projectile = new GameObject("MagicProjectile");
        
        // Add components
        projectile.AddComponent<Projectile>();
        
        Rigidbody2D rb = projectile.AddComponent<Rigidbody2D>();
        rb.gravityScale = 0f;
        
        CircleCollider2D col = projectile.AddComponent<CircleCollider2D>();
        col.radius = 0.1f;
        col.isTrigger = true;
        
        // Add sprite renderer
        SpriteRenderer sr = projectile.AddComponent<SpriteRenderer>();
        sr.sprite = CreateSimpleSprite(Color.yellow, "Projectile");
        
        // Save as prefab
        SaveAsPrefab(projectile, "Assets/Resources/Prefabs/MagicProjectile.prefab");
        
        // Clean up
        DestroyImmediate(projectile);
        
        Debug.Log("QuickSetup: MagicProjectile prefab created!");
    }
    
    void CreateExperienceGemPrefab()
    {
        GameObject existingPrefab = Resources.Load<GameObject>("Prefabs/ExperienceGem");
        if (existingPrefab != null)
        {
            Debug.Log("QuickSetup: ExperienceGem prefab already exists.");
            return;
        }
        
        Debug.Log("QuickSetup: Creating ExperienceGem prefab...");
        
        GameObject gem = new GameObject("ExperienceGem");
        
        // Add components
        gem.AddComponent<ExperienceGem>();
        
        CircleCollider2D col = gem.AddComponent<CircleCollider2D>();
        col.radius = 0.3f;
        col.isTrigger = true;
        
        // Add sprite renderer
        SpriteRenderer sr = gem.AddComponent<SpriteRenderer>();
        sr.sprite = CreateSimpleSprite(Color.green, "Gem");
        
        // Save as prefab
        SaveAsPrefab(gem, "Assets/Resources/Prefabs/ExperienceGem.prefab");
        
        // Clean up
        DestroyImmediate(gem);
        
        Debug.Log("QuickSetup: ExperienceGem prefab created!");
    }
    
    void SetupGameManager()
    {
        GameManager existing = FindObjectOfType<GameManager>();
        if (existing == null)
        {
            Debug.Log("QuickSetup: Creating GameManager...");
            
            GameObject gm = new GameObject("GameManager");
            gm.AddComponent<GameManager>();
            
            Debug.Log("QuickSetup: GameManager created!");
        }
        else
        {
            Debug.Log("QuickSetup: GameManager already exists.");
        }
    }
    
    void SetupEnemySpawner()
    {
        EnemySpawner existing = FindObjectOfType<EnemySpawner>();
        if (existing == null)
        {
            Debug.Log("QuickSetup: Creating EnemySpawner...");
            
            GameObject spawner = new GameObject("EnemySpawner");
            EnemySpawner spawnerScript = spawner.AddComponent<EnemySpawner>();
            
            // Load the enemy prefab and assign it
            GameObject enemyPrefab = Resources.Load<GameObject>("Prefabs/BasicEnemy");
            if (enemyPrefab != null)
            {
                spawnerScript.enemyPrefabs = new GameObject[] { enemyPrefab };
            }
            
            Debug.Log("QuickSetup: EnemySpawner created!");
        }
        else
        {
            Debug.Log("QuickSetup: EnemySpawner already exists.");
        }
    }
    
    Sprite CreateSimpleSprite(Color color, string name)
    {
        // Create a simple 32x32 colored texture
        Texture2D texture = new Texture2D(32, 32);
        Color[] pixels = new Color[32 * 32];
        for (int i = 0; i < pixels.Length; i++)
        {
            pixels[i] = color;
        }
        texture.SetPixels(pixels);
        texture.Apply();
        
        return Sprite.Create(texture, new Rect(0, 0, 32, 32), new Vector2(0.5f, 0.5f));
    }
    
    void SaveAsPrefab(GameObject obj, string path)
    {
        #if UNITY_EDITOR
        // Ensure directory exists
        string directory = System.IO.Path.GetDirectoryName(path);
        if (!System.IO.Directory.Exists(directory))
        {
            System.IO.Directory.CreateDirectory(directory);
        }
        
        PrefabUtility.SaveAsPrefabAsset(obj, path);
        #endif
    }
}
