using UnityEditor;
using System.IO;
using UnityEngine;

public class CreateFolderStructure : EditorWindow
{
    [MenuItem("Tools/Create Project Folders")]
    public static void CreateFolders()
    {
        string projectPath = "Assets/";

        string[] folders = new string[] {
            "Scripts",
            "Scripts/Player",
            "Scripts/Weapons",
            "Scripts/Enemies",
            "Scripts/Items",
            "Scripts/GameManager",
            "Scripts/UI",
            "Scripts/Core",
            "Scripts/Utils",
            "Sprites",
            "Prefabs",
            "Audio",
            "Scenes",
            "Animations"
        };

        foreach (string folder in folders)
        {
            string path = Path.Combine(projectPath, folder);
            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
                Debug.Log("Created folder: " + path);
            }
            else
            {
                Debug.Log("Folder already exists: " + path);
            }
        }

        AssetDatabase.Refresh(); // Refresh the AssetDatabase to show new folders in Unity editor
        EditorUtility.DisplayDialog("Create Project Folders", "Project folder structure created successfully (or already existed)!", "OK");
    }
}