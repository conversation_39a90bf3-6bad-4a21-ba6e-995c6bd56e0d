using UnityEngine;

public class ManualGameSetup : MonoBehaviour
{
    [Header("Manual Setup - Drag and Drop")]
    public GameObject playerPrefab;
    public GameObject enemyPrefab;
    public GameObject projectilePrefab;
    public GameObject experienceGemPrefab;
    
    [Header("Setup Controls")]
    public bool createPlayerOnStart = true;
    public bool createGameManagerOnStart = true;
    public bool createEnemySpawnerOnStart = true;
    
    void Start()
    {
        Debug.Log("ManualGameSetup: Starting setup...");
        
        if (createPlayerOnStart)
        {
            CreatePlayer();
        }
        
        if (createGameManagerOnStart)
        {
            CreateGameManager();
        }
        
        if (createEnemySpawnerOnStart)
        {
            CreateEnemySpawner();
        }
        
        // Create some test enemies
        Invoke("CreateTestEnemies", 1f);
    }
    
    void CreatePlayer()
    {
        GameObject existingPlayer = GameObject.FindGameObjectWithTag("Player");
        if (existingPlayer != null)
        {
            Debug.Log("Player already exists!");
            return;
        }
        
        Debug.Log("Creating player manually...");
        
        // Create player object
        GameObject player = new GameObject("Player");
        player.tag = "Player";
        player.transform.position = Vector3.zero;
        
        // Add PlayerController
        PlayerController pc = player.AddComponent<PlayerController>();
        pc.moveSpeed = 5f;
        pc.maxHealth = 100;
        pc.currentHealth = 100;
        
        // Add PlayerLevel
        PlayerLevel pl = player.AddComponent<PlayerLevel>();
        pl.currentLevel = 1;
        pl.currentExperience = 0;
        pl.experienceToNextLevel = 100;
        
        // Add physics
        Rigidbody2D rb = player.AddComponent<Rigidbody2D>();
        rb.gravityScale = 0f;
        rb.freezeRotation = true;
        
        // Add collider
        CircleCollider2D col = player.AddComponent<CircleCollider2D>();
        col.radius = 0.5f;
        
        // Add visual
        SpriteRenderer sr = player.AddComponent<SpriteRenderer>();
        sr.sprite = CreateColoredSprite(Color.blue, 64);
        sr.sortingOrder = 1;
        
        // Create weapon as child
        GameObject weaponObj = new GameObject("MagicWand");
        weaponObj.transform.SetParent(player.transform);
        
        MagicWand weapon = weaponObj.AddComponent<MagicWand>();
        weapon.baseDamage = 25f;
        weapon.baseCooldown = 0.5f;
        weapon.projectileSpeed = 15f;
        weapon.findTargetRange = 15f;
        
        // Create projectile prefab if needed
        if (weapon.projectilePrefab == null)
        {
            weapon.projectilePrefab = CreateProjectilePrefab();
        }
        
        Debug.Log("Player created successfully!");
    }
    
    GameObject CreateProjectilePrefab()
    {
        Debug.Log("Creating projectile prefab...");
        
        GameObject projectile = new GameObject("MagicProjectile");
        
        // Add Projectile script
        Projectile proj = projectile.AddComponent<Projectile>();
        proj.speed = 15f;
        proj.damage = 25f;
        proj.lifetime = 3f;
        proj.pierceCount = 0;
        
        // Add physics
        Rigidbody2D rb = projectile.AddComponent<Rigidbody2D>();
        rb.gravityScale = 0f;
        
        // Add trigger collider
        CircleCollider2D col = projectile.AddComponent<CircleCollider2D>();
        col.radius = 0.1f;
        col.isTrigger = true;
        
        // Add visual
        SpriteRenderer sr = projectile.AddComponent<SpriteRenderer>();
        sr.sprite = CreateColoredSprite(Color.yellow, 16);
        sr.sortingOrder = 2;
        
        return projectile;
    }
    
    void CreateGameManager()
    {
        GameManager existing = FindObjectOfType<GameManager>();
        if (existing != null)
        {
            Debug.Log("GameManager already exists!");
            return;
        }
        
        Debug.Log("Creating GameManager...");
        
        GameObject gm = new GameObject("GameManager");
        GameManager gmScript = gm.AddComponent<GameManager>();
        gmScript.gameDurationMinutes = 30f;
        gmScript.startGameAutomatically = true;
        
        Debug.Log("GameManager created!");
    }
    
    void CreateEnemySpawner()
    {
        EnemySpawner existing = FindObjectOfType<EnemySpawner>();
        if (existing != null)
        {
            Debug.Log("EnemySpawner already exists!");
            return;
        }
        
        Debug.Log("Creating EnemySpawner...");
        
        GameObject spawnerObj = new GameObject("EnemySpawner");
        EnemySpawner spawner = spawnerObj.AddComponent<EnemySpawner>();
        
        // Create enemy prefab
        GameObject enemyPrefab = CreateEnemyPrefab();
        spawner.enemyPrefabs = new GameObject[] { enemyPrefab };
        
        // Find player
        GameObject player = GameObject.FindGameObjectWithTag("Player");
        if (player != null)
        {
            spawner.playerTransform = player.transform;
        }
        
        // Set spawner parameters
        spawner.spawnRadius = 20f;
        spawner.minSpawnDistance = 10f;
        spawner.baseSpawnInterval = 2f;
        spawner.maxEnemiesOnScreen = 50;
        
        Debug.Log("EnemySpawner created!");
    }
    
    GameObject CreateEnemyPrefab()
    {
        Debug.Log("Creating enemy prefab...");
        
        GameObject enemy = new GameObject("BasicEnemy");
        enemy.tag = "Enemy";
        
        // Add BasicEnemy script
        BasicEnemy enemyScript = enemy.AddComponent<BasicEnemy>();
        
        // Add physics
        Rigidbody2D rb = enemy.AddComponent<Rigidbody2D>();
        rb.gravityScale = 0f;
        rb.freezeRotation = true;
        
        // Add collider
        CircleCollider2D col = enemy.AddComponent<CircleCollider2D>();
        col.radius = 0.4f;
        
        // Add visual
        SpriteRenderer sr = enemy.AddComponent<SpriteRenderer>();
        sr.sprite = CreateColoredSprite(Color.red, 48);
        sr.sortingOrder = 1;
        
        return enemy;
    }
    
    void CreateTestEnemies()
    {
        Debug.Log("Creating test enemies...");
        
        GameObject player = GameObject.FindGameObjectWithTag("Player");
        if (player == null)
        {
            Debug.Log("No player found for test enemies!");
            return;
        }
        
        // Create a few test enemies around the player
        for (int i = 0; i < 3; i++)
        {
            float angle = i * 120f * Mathf.Deg2Rad;
            Vector3 spawnPos = player.transform.position + new Vector3(
                Mathf.Cos(angle) * 8f,
                Mathf.Sin(angle) * 8f,
                0f
            );
            
            GameObject enemy = CreateEnemyPrefab();
            enemy.transform.position = spawnPos;
            enemy.name = $"TestEnemy_{i}";
            
            Debug.Log($"Test enemy {i} created at {spawnPos}");
        }
    }
    
    Sprite CreateColoredSprite(Color color, int size)
    {
        Texture2D texture = new Texture2D(size, size);
        Color[] pixels = new Color[size * size];
        
        // Create a simple circle
        Vector2 center = new Vector2(size / 2f, size / 2f);
        float radius = size / 2f - 2f;
        
        for (int x = 0; x < size; x++)
        {
            for (int y = 0; y < size; y++)
            {
                float distance = Vector2.Distance(new Vector2(x, y), center);
                if (distance <= radius)
                {
                    pixels[y * size + x] = color;
                }
                else
                {
                    pixels[y * size + x] = Color.clear;
                }
            }
        }
        
        texture.SetPixels(pixels);
        texture.Apply();
        
        return Sprite.Create(texture, new Rect(0, 0, size, size), new Vector2(0.5f, 0.5f));
    }
    
    void Update()
    {
        // Debug controls
        if (Input.GetKeyDown(KeyCode.T))
        {
            CreateTestEnemies();
        }
        
        if (Input.GetKeyDown(KeyCode.P))
        {
            // Test projectile creation
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                MagicWand weapon = player.GetComponentInChildren<MagicWand>();
                if (weapon != null)
                {
                    Debug.Log($"Weapon found! Projectile prefab: {weapon.projectilePrefab != null}");
                }
            }
        }
    }
    
    void OnGUI()
    {
        GUILayout.BeginArea(new Rect(Screen.width - 200, 10, 190, 100));
        GUILayout.Label("Manual Setup Controls:");
        GUILayout.Label("T - Create test enemies");
        GUILayout.Label("P - Check weapon status");
        GUILayout.EndArea();
    }
}
