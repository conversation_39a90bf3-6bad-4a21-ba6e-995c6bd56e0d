using UnityEngine;

public class DebugHelper : MonoBehaviour
{
    [Header("Debug Information")]
    public bool showDebugInfo = true;
    
    void Start()
    {
        if (showDebugInfo)
        {
            LogSceneInfo();
        }
    }
    
    void Update()
    {
        if (Input.GetKeyDown(KeyCode.F1))
        {
            LogSceneInfo();
        }
        
        if (Input.GetKeyDown(KeyCode.F2))
        {
            CreateTestEnemy();
        }
        
        if (Input.GetKeyDown(KeyCode.F3))
        {
            TestWeaponFiring();
        }
    }
    
    void LogSceneInfo()
    {
        Debug.Log("=== SCENE DEBUG INFO ===");
        
        // Check for player
        GameObject player = GameObject.FindGameObjectWithTag("Player");
        if (player != null)
        {
            Debug.Log($"✓ Player found: {player.name}");
            PlayerController pc = player.GetComponent<PlayerController>();
            PlayerLevel pl = player.GetComponent<PlayerLevel>();
            MagicWand mw = player.GetComponentInChildren<MagicWand>();
            
            Debug.Log($"  - PlayerController: {(pc != null ? "✓" : "✗")}");
            Debug.Log($"  - PlayerLevel: {(pl != null ? "✓" : "✗")}");
            Debug.Log($"  - MagicWand: {(mw != null ? "✓" : "✗")}");
            
            if (mw != null)
            {
                Debug.Log($"  - MagicWand projectile prefab: {(mw.projectilePrefab != null ? "✓" : "✗")}");
            }
        }
        else
        {
            Debug.Log("✗ No Player found!");
        }
        
        // Check for enemies
        GameObject[] enemies = GameObject.FindGameObjectsWithTag("Enemy");
        Debug.Log($"Enemies in scene: {enemies.Length}");
        
        // Check for GameManager
        GameManager gm = FindObjectOfType<GameManager>();
        Debug.Log($"GameManager: {(gm != null ? "✓" : "✗")}");
        
        // Check for EnemySpawner
        EnemySpawner es = FindObjectOfType<EnemySpawner>();
        Debug.Log($"EnemySpawner: {(es != null ? "✓" : "✗")}");
        if (es != null)
        {
            Debug.Log($"  - Enemy prefabs assigned: {(es.enemyPrefabs != null && es.enemyPrefabs.Length > 0 ? "✓" : "✗")}");
            Debug.Log($"  - Player transform assigned: {(es.playerTransform != null ? "✓" : "✗")}");
        }
        
        // Check for prefabs
        GameObject enemyPrefab = Resources.Load<GameObject>("Prefabs/BasicEnemy");
        GameObject projectilePrefab = Resources.Load<GameObject>("Prefabs/MagicProjectile");
        GameObject gemPrefab = Resources.Load<GameObject>("Prefabs/ExperienceGem");
        
        Debug.Log($"BasicEnemy prefab: {(enemyPrefab != null ? "✓" : "✗")}");
        Debug.Log($"MagicProjectile prefab: {(projectilePrefab != null ? "✓" : "✗")}");
        Debug.Log($"ExperienceGem prefab: {(gemPrefab != null ? "✗" : "✗")}");
        
        Debug.Log("=== END DEBUG INFO ===");
        Debug.Log("Press F1 to refresh info, F2 to create test enemy, F3 to test weapon");
    }
    
    void CreateTestEnemy()
    {
        GameObject player = GameObject.FindGameObjectWithTag("Player");
        if (player == null)
        {
            Debug.Log("No player found to spawn enemy near!");
            return;
        }
        
        // Try to load enemy prefab
        GameObject enemyPrefab = Resources.Load<GameObject>("Prefabs/BasicEnemy");
        if (enemyPrefab != null)
        {
            Vector3 spawnPos = player.transform.position + new Vector3(5, 0, 0);
            GameObject enemy = Instantiate(enemyPrefab, spawnPos, Quaternion.identity);
            Debug.Log($"Test enemy created at {spawnPos}");
        }
        else
        {
            // Create enemy manually
            GameObject enemy = new GameObject("TestEnemy");
            enemy.tag = "Enemy";
            enemy.transform.position = player.transform.position + new Vector3(5, 0, 0);
            
            // Add components
            enemy.AddComponent<BasicEnemy>();
            
            Rigidbody2D rb = enemy.AddComponent<Rigidbody2D>();
            rb.gravityScale = 0f;
            rb.freezeRotation = true;
            
            CircleCollider2D col = enemy.AddComponent<CircleCollider2D>();
            col.radius = 0.4f;
            
            // Add simple red sprite
            SpriteRenderer sr = enemy.AddComponent<SpriteRenderer>();
            sr.sprite = CreateSimpleSprite(Color.red);
            
            Debug.Log("Manual test enemy created!");
        }
    }
    
    void TestWeaponFiring()
    {
        GameObject player = GameObject.FindGameObjectWithTag("Player");
        if (player == null)
        {
            Debug.Log("No player found!");
            return;
        }
        
        MagicWand weapon = player.GetComponentInChildren<MagicWand>();
        if (weapon == null)
        {
            Debug.Log("No MagicWand found on player!");
            return;
        }
        
        // Check if there are enemies to target
        GameObject[] enemies = GameObject.FindGameObjectsWithTag("Enemy");
        if (enemies.Length == 0)
        {
            Debug.Log("No enemies found for weapon to target!");
            CreateTestEnemy();
            return;
        }
        
        Debug.Log($"Weapon found! Should be targeting {enemies.Length} enemies.");
        Debug.Log($"Weapon projectile prefab: {(weapon.projectilePrefab != null ? "assigned" : "missing")}");
    }
    
    Sprite CreateSimpleSprite(Color color)
    {
        Texture2D texture = new Texture2D(32, 32);
        Color[] pixels = new Color[32 * 32];
        for (int i = 0; i < pixels.Length; i++)
        {
            pixels[i] = color;
        }
        texture.SetPixels(pixels);
        texture.Apply();
        
        return Sprite.Create(texture, new Rect(0, 0, 32, 32), new Vector2(0.5f, 0.5f));
    }
    
    void OnGUI()
    {
        if (!showDebugInfo) return;
        
        GUILayout.BeginArea(new Rect(10, Screen.height - 150, 300, 140));
        GUILayout.Label("=== DEBUG CONTROLS ===");
        GUILayout.Label("F1 - Show scene info");
        GUILayout.Label("F2 - Create test enemy");
        GUILayout.Label("F3 - Test weapon");
        GUILayout.Label("WASD - Move player");
        
        if (GUILayout.Button("Force Create Test Enemy"))
        {
            CreateTestEnemy();
        }
        
        if (GUILayout.Button("Log Scene Info"))
        {
            LogSceneInfo();
        }
        
        GUILayout.EndArea();
    }
}
