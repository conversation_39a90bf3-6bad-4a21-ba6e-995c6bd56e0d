using UnityEngine;

public class PlayerController : MonoBehaviour
{
    [Head<PERSON>("Movement Settings")]
    public float moveSpeed = 5f;

    [<PERSON><PERSON>("Player Stats")]
    public int maxHealth = 100;
    public int currentHealth;

    private Rigidbody2D rb;
    private Vector2 movementInput;

    void Start()
    {
        rb = GetComponent<Rigidbody2D>();
        if (rb == null)
        {
            Debug.Log<PERSON>rror("PlayerController on " + gameObject.name + " requires a Rigidbody2D component to be attached!");
        }
        currentHealth = maxHealth;
    }

    void Update()
    {
        movementInput.x = Input.GetAxisRaw("Horizontal");
        movementInput.y = Input.GetAxisRaw("Vertical");

        if (movementInput.sqrMagnitude > 1)
        {
            movementInput.Normalize();
        }

        if (currentHealth <= 0)
        {
            Die();
        }
    }

    void FixedUpdate()
    {
        if (rb != null)
        {
            rb.MovePosition(rb.position + movementInput * moveSpeed * Time.fixedDeltaTime);
        }
    }

    public void TakeDamage(int damageAmount)
    {
        currentHealth -= damageAmount;
        Debug.Log(gameObject.name + " took " + damageAmount + " damage. Current health: " + currentHealth);

        if (currentHealth < 0)
        {
            currentHealth = 0;
        }
    }

    void Die()
    {
        Debug.Log(gameObject.name + " has died!");
        this.enabled = false; 
        // Consider FindObjectOfType<GameManager>()?.HandlePlayerDeath();
    }

    public void Heal(int healAmount)
    {
        currentHealth += healAmount;
        if (currentHealth > maxHealth)
        {
            currentHealth = maxHealth;
        }
        Debug.Log(gameObject.name + " healed " + healAmount + " HP. Current health: " + currentHealth);
    }
}