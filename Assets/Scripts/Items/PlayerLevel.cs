using UnityEngine;
using System; // Required for Action

public class PlayerLevel : MonoBehaviour
{
    [Header("Leveling Stats")]
    public int currentLevel = 1;
    public int currentExperience = 0;
    public int experienceToNextLevel = 100; // XP needed for the first level up
    public float experienceMultiplier = 1.2f; // How much the XP needed increases each level

    [Header("Debug")]
    [SerializeField] // Shows in inspector but not public
    private int baseExperienceToNextLevel; // To store the initial value for calculation

    // Event to notify other systems when the player levels up
    // Interested systems (like an Upgrade UI manager) can subscribe to this.
    public static event Action OnPlayerLevelUp;
    
    // Event to notify UI or other systems about XP change
    // Parameters: currentXP, xpToReachNextLevel
    public event Action<int, int> OnExperienceChanged; 
    
    // Event to notify UI or other systems about Level change
    // Parameter: newLevel
    public event Action<int> OnLevelChanged; 

    void Start()
    {
        // Store the initial XP requirement for level 2, so scaling is consistent.
        baseExperienceToNextLevel = experienceToNextLevel;
        
        // Initialize UI (if any is listening)
        UpdateExperienceUI();
        UpdateLevelUI();
    }

    public void AddExperience(int amount)
    {
        if (amount <= 0) return; // No negative or zero XP gain

        currentExperience += amount;
        Debug.Log($"Player gained {amount} XP. Current XP: {currentExperience}/{experienceToNextLevel}");

        // Loop in case enough XP is gained to level up multiple times
        while (currentExperience >= experienceToNextLevel)
        {
            LevelUp();
        }
        UpdateExperienceUI(); // Update UI after potential level ups and XP changes
    }

    private void LevelUp()
    {
        currentExperience -= experienceToNextLevel; // Subtract XP needed for the current level, carry over any excess XP
        currentLevel++;
        
        // Calculate XP needed for the *new* next level
        // Example of scaling: each level requires 'multiplier' times more than the base for level 1.
        // Or, 'multiplier' times more than the *previous* level's requirement.
        // Current formula: scales based on the initial 'experienceToNextLevel' value.
        experienceToNextLevel = Mathf.FloorToInt(baseExperienceToNextLevel * Mathf.Pow(experienceMultiplier, currentLevel - 1));
        // Alternative scaling: each level is 'multiplier' * previous level's requirement
        // experienceToNextLevel = Mathf.FloorToInt(experienceToNextLevel * experienceMultiplier); 

        Debug.Log($"Player Leveled Up to Level {currentLevel}! Next level in {experienceToNextLevel} XP. Excess XP carried over: {currentExperience}");

        OnPlayerLevelUp?.Invoke(); // Signal that a level up occurred.
        UpdateLevelUI(); // Update level display.

        // The AddExperience's while loop will re-evaluate if the carried-over XP is enough for another level.
    }

    private void UpdateExperienceUI()
    {
        // Notify any listeners (like a UI ProgressBar) about the current XP status.
        OnExperienceChanged?.Invoke(currentExperience, experienceToNextLevel);
    }

    private void UpdateLevelUI()
    {
        // Notify any listeners (like a UI Text field) about the new level.
        OnLevelChanged?.Invoke(currentLevel);
    }

    // A utility function for debugging or for granting a level via a cheat command.
    public void ForceLevelUpForTesting()
    {
        // Give exactly enough XP to trigger the next level up.
        AddExperience(experienceToNextLevel - currentExperience);
    }
}