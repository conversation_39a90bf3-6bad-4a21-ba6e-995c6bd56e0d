using UnityEngine;
using System.Collections.Generic;

public class ComponentEnemySpawner : MonoBehaviour
{
    [Header("Component-Based Spawning")]
    public Transform playerTransform;
    public float spawnRadius = 15f;
    public float minSpawnDistance = 8f;
    public float spawnInterval = 2f;
    public int maxEnemies = 25;
    
    private float spawnTimer;
    private List<GameObject> activeEnemies = new List<GameObject>();

    void Start()
    {
        // Find player by component instead of tag
        if (playerTransform == null)
        {
            PlayerController player = FindObjectOfType<PlayerController>();
            if (player != null)
            {
                playerTransform = player.transform;
                Debug.Log("ComponentEnemySpawner: Player found by component");
            }
            else
            {
                Debug.LogError("ComponentEnemySpawner: No PlayerController found!");
                enabled = false;
                return;
            }
        }

        spawnTimer = spawnInterval;
        Debug.Log("ComponentEnemySpawner: Initialized successfully (no tags required)");
    }

    void Update()
    {
        if (playerTransform == null) return;

        spawnTimer -= Time.deltaTime;

        if (spawnTimer <= 0f)
        {
            CleanupDeadEnemies();
            
            if (activeEnemies.Count < maxEnemies)
            {
                SpawnEnemy();
            }
            
            spawnTimer = spawnInterval;
        }
    }

    void SpawnEnemy()
    {
        Vector2 spawnPosition = GetRandomSpawnPosition();
        GameObject enemy = CreateEnemyAtPosition(spawnPosition);
        
        if (enemy != null)
        {
            activeEnemies.Add(enemy);
            Debug.Log($"Enemy spawned at {spawnPosition}. Total enemies: {activeEnemies.Count}");
        }
    }

    GameObject CreateEnemyAtPosition(Vector2 position)
    {
        GameObject enemy = new GameObject("SpawnedEnemy");
        enemy.transform.position = position;
        
        // No tag setting - use components for identification
        
        // Add BasicEnemy script
        BasicEnemy enemyScript = enemy.AddComponent<BasicEnemy>();
        
        // Add physics
        Rigidbody2D rb = enemy.AddComponent<Rigidbody2D>();
        rb.gravityScale = 0f;
        rb.freezeRotation = true;
        
        // Add collider
        CircleCollider2D col = enemy.AddComponent<CircleCollider2D>();
        col.radius = 0.4f;
        
        // Add visual
        SpriteRenderer sr = enemy.AddComponent<SpriteRenderer>();
        sr.sprite = CreateEnemySprite();
        sr.sortingOrder = 1;
        
        return enemy;
    }

    Vector2 GetRandomSpawnPosition()
    {
        Vector2 playerPos = playerTransform.position;
        Vector2 spawnPos;
        int attempts = 0;
        int maxAttempts = 20;

        do
        {
            float angle = Random.Range(0f, 360f) * Mathf.Deg2Rad;
            float distance = Random.Range(minSpawnDistance, spawnRadius);
            
            spawnPos = playerPos + new Vector2(
                Mathf.Cos(angle) * distance,
                Mathf.Sin(angle) * distance
            );
            
            attempts++;
        }
        while (Vector2.Distance(spawnPos, playerPos) < minSpawnDistance && attempts < maxAttempts);

        return spawnPos;
    }

    void CleanupDeadEnemies()
    {
        activeEnemies.RemoveAll(enemy => enemy == null);
    }

    Sprite CreateEnemySprite()
    {
        int size = 48;
        Texture2D texture = new Texture2D(size, size);
        Color[] pixels = new Color[size * size];
        
        Vector2 center = new Vector2(size / 2f, size / 2f);
        float radius = size / 2f - 2f;
        
        for (int x = 0; x < size; x++)
        {
            for (int y = 0; y < size; y++)
            {
                float distance = Vector2.Distance(new Vector2(x, y), center);
                if (distance <= radius)
                {
                    pixels[y * size + x] = Color.red;
                }
                else
                {
                    pixels[y * size + x] = Color.clear;
                }
            }
        }
        
        texture.SetPixels(pixels);
        texture.Apply();
        
        return Sprite.Create(texture, new Rect(0, 0, size, size), new Vector2(0.5f, 0.5f));
    }

    public int GetActiveEnemyCount()
    {
        CleanupDeadEnemies();
        return activeEnemies.Count;
    }

    public void ClearAllEnemies()
    {
        foreach (GameObject enemy in activeEnemies)
        {
            if (enemy != null)
            {
                Destroy(enemy);
            }
        }
        activeEnemies.Clear();
    }

    void OnDrawGizmosSelected()
    {
        if (playerTransform == null) return;

        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(playerTransform.position, spawnRadius);

        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(playerTransform.position, minSpawnDistance);
    }
}
