using UnityEngine;

public class BasicEnemy : Enemy
{
    [Header("Basic Enemy Specifics")]
    public float wanderRadius = 2f; // Radius for random wandering when no player
    public float wanderTimer = 3f; // Time between wander direction changes
    
    private Vector2 wanderTarget;
    private float currentWanderTimer;
    private bool isWandering = false;

    protected override void Start()
    {
        base.Start();
        
        // Set basic enemy stats
        enemyName = "Basic Enemy";
        maxHealth = 30;
        currentHealth = maxHealth;
        moveSpeed = 1.5f;
        damage = 5;
        experienceValue = 5;
        attackRange = 0.8f;
        attackCooldown = 1.5f;

        // Initialize wandering
        currentWanderTimer = wanderTimer;
        SetNewWanderTarget();
    }

    protected override void Update()
    {
        if (isDead) return;

        base.Update(); // Call parent Update for basic enemy behavior

        // If player is too far away, wander around
        if (playerTransform != null)
        {
            float distanceToPlayer = Vector2.Distance(transform.position, playerTransform.position);
            
            // If player is very far away, wander instead of chasing
            if (distanceToPlayer > 15f) // Wander if player is more than 15 units away
            {
                HandleWandering();
            }
            else
            {
                isWandering = false;
            }
        }
        else
        {
            // No player found, just wander
            HandleWandering();
        }
    }

    private void HandleWandering()
    {
        isWandering = true;
        currentWanderTimer -= Time.deltaTime;

        if (currentWanderTimer <= 0f)
        {
            SetNewWanderTarget();
            currentWanderTimer = wanderTimer;
        }

        // Move towards wander target
        transform.position = Vector2.MoveTowards(transform.position, wanderTarget, moveSpeed * 0.5f * Time.deltaTime);
        
        // Face movement direction
        Vector2 direction = (wanderTarget - (Vector2)transform.position).normalized;
        if (direction.x != 0)
        {
            transform.localScale = new Vector3(direction.x > 0 ? 1 : -1, 1, 1);
        }
    }

    private void SetNewWanderTarget()
    {
        // Pick a random point within wander radius
        Vector2 randomDirection = Random.insideUnitCircle * wanderRadius;
        wanderTarget = (Vector2)transform.position + randomDirection;
    }

    protected override void MoveTowardsPlayer()
    {
        if (isWandering) return; // Don't chase player if wandering
        
        base.MoveTowardsPlayer();
    }

    protected override void OnAttackPerformed()
    {
        base.OnAttackPerformed();
        
        // Basic enemy specific attack effects
        // For example, you could add a simple attack animation or sound here
        Debug.Log($"{enemyName} performed a basic melee attack!");
    }

    protected override void OnDamageReceived(float damageAmount)
    {
        base.OnDamageReceived(damageAmount);
        
        // Basic enemy specific damage effects
        // For example, flash the sprite red briefly
        StartCoroutine(FlashRed());
    }

    private System.Collections.IEnumerator FlashRed()
    {
        SpriteRenderer spriteRenderer = GetComponent<SpriteRenderer>();
        if (spriteRenderer != null)
        {
            Color originalColor = spriteRenderer.color;
            spriteRenderer.color = Color.red;
            yield return new WaitForSeconds(0.1f);
            spriteRenderer.color = originalColor;
        }
    }

    protected override void OnDeathEffect()
    {
        base.OnDeathEffect();
        
        // Basic enemy specific death effects
        Debug.Log($"{enemyName} died with a basic death effect!");
        
        // You could add particle effects, sound, etc. here
        // For example:
        // if (deathParticlesPrefab != null)
        // {
        //     Instantiate(deathParticlesPrefab, transform.position, Quaternion.identity);
        // }
    }

    // Draw wander radius in Scene view for debugging
    protected override void OnDrawGizmosSelected()
    {
        base.OnDrawGizmosSelected(); // Draw attack range
        
        // Draw wander radius
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, wanderRadius);
        
        // Draw current wander target
        if (isWandering)
        {
            Gizmos.color = Color.green;
            Gizmos.DrawWireSphere(wanderTarget, 0.3f);
            Gizmos.DrawLine(transform.position, wanderTarget);
        }
    }
}
