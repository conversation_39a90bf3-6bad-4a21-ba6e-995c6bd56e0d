using UnityEngine;
using System.Collections.Generic;

public class EnemySpawner : MonoBehaviour
{
    [Header("Spawning Settings")]
    public GameObject[] enemyPrefabs; // Array of enemy prefabs to spawn
    public Transform playerTransform; // Reference to player for spawn positioning
    public float spawnRadius = 20f; // Distance from player to spawn enemies
    public float minSpawnDistance = 15f; // Minimum distance from player to spawn
    
    [Header("Spawn Timing")]
    public float baseSpawnInterval = 2f; // Base time between spawns
    public float minSpawnInterval = 0.3f; // Minimum spawn interval (max difficulty)
    public float difficultyIncreaseRate = 0.95f; // Multiplier for spawn interval each wave
    
    [Header("Enemy Counts")]
    public int maxEnemiesOnScreen = 100; // Maximum enemies allowed at once
    public int baseEnemiesPerWave = 1; // Base number of enemies per spawn
    public int maxEnemiesPerWave = 5; // Maximum enemies per spawn
    
    [Header("Difficulty Progression")]
    public float gameTimeForMaxDifficulty = 1800f; // 30 minutes in seconds
    
    private float currentSpawnInterval;
    private float spawnTimer;
    private int currentEnemiesPerWave;
    private List<GameObject> activeEnemies = new List<GameObject>();
    private float gameStartTime;

    void Start()
    {
        // Find player if not assigned
        if (playerTransform == null)
        {
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                playerTransform = player.transform;
            }
            else
            {
                Debug.LogError("EnemySpawner: Player not found! Make sure player has 'Player' tag.");
                enabled = false;
                return;
            }
        }

        // Validate enemy prefabs
        if (enemyPrefabs == null || enemyPrefabs.Length == 0)
        {
            Debug.LogError("EnemySpawner: No enemy prefabs assigned!");
            enabled = false;
            return;
        }

        // Initialize spawning variables
        currentSpawnInterval = baseSpawnInterval;
        currentEnemiesPerWave = baseEnemiesPerWave;
        spawnTimer = currentSpawnInterval;
        gameStartTime = Time.time;

        // Subscribe to enemy death events to track active enemies
        Enemy.OnEnemyDeath += OnEnemyDied;
    }

    void OnDestroy()
    {
        // Unsubscribe from events
        Enemy.OnEnemyDeath -= OnEnemyDied;
    }

    void Update()
    {
        if (playerTransform == null) return;

        // Update spawn timer
        spawnTimer -= Time.deltaTime;

        // Spawn enemies when timer reaches zero
        if (spawnTimer <= 0f)
        {
            SpawnWave();
            ResetSpawnTimer();
        }

        // Update difficulty based on game time
        UpdateDifficulty();

        // Clean up null references in active enemies list
        CleanupActiveEnemiesList();
    }

    void SpawnWave()
    {
        // Don't spawn if we're at the enemy limit
        if (activeEnemies.Count >= maxEnemiesOnScreen)
        {
            Debug.Log("EnemySpawner: Max enemies on screen reached, skipping spawn.");
            return;
        }

        // Calculate how many enemies to spawn this wave
        int enemiesToSpawn = Mathf.Min(currentEnemiesPerWave, maxEnemiesOnScreen - activeEnemies.Count);

        for (int i = 0; i < enemiesToSpawn; i++)
        {
            SpawnSingleEnemy();
        }

        Debug.Log($"EnemySpawner: Spawned {enemiesToSpawn} enemies. Total active: {activeEnemies.Count}");
    }

    void SpawnSingleEnemy()
    {
        // Choose random enemy prefab
        GameObject enemyPrefab = enemyPrefabs[Random.Range(0, enemyPrefabs.Length)];
        
        // Find spawn position around player
        Vector2 spawnPosition = GetRandomSpawnPosition();
        
        // Spawn the enemy
        GameObject newEnemy = Instantiate(enemyPrefab, spawnPosition, Quaternion.identity);
        
        // Make sure it has the Enemy tag
        if (!newEnemy.CompareTag("Enemy"))
        {
            newEnemy.tag = "Enemy";
        }
        
        // Add to active enemies list
        activeEnemies.Add(newEnemy);
    }

    Vector2 GetRandomSpawnPosition()
    {
        Vector2 playerPos = playerTransform.position;
        Vector2 spawnPos;
        int attempts = 0;
        int maxAttempts = 30;

        do
        {
            // Generate random angle
            float angle = Random.Range(0f, 360f) * Mathf.Deg2Rad;
            
            // Generate random distance between min and max spawn distance
            float distance = Random.Range(minSpawnDistance, spawnRadius);
            
            // Calculate spawn position
            spawnPos = playerPos + new Vector2(
                Mathf.Cos(angle) * distance,
                Mathf.Sin(angle) * distance
            );
            
            attempts++;
        }
        while (Vector2.Distance(spawnPos, playerPos) < minSpawnDistance && attempts < maxAttempts);

        return spawnPos;
    }

    void ResetSpawnTimer()
    {
        spawnTimer = currentSpawnInterval;
    }

    void UpdateDifficulty()
    {
        float gameTime = Time.time - gameStartTime;
        float difficultyProgress = Mathf.Clamp01(gameTime / gameTimeForMaxDifficulty);

        // Decrease spawn interval over time (spawn faster)
        currentSpawnInterval = Mathf.Lerp(baseSpawnInterval, minSpawnInterval, difficultyProgress);

        // Increase enemies per wave over time
        currentEnemiesPerWave = Mathf.RoundToInt(Mathf.Lerp(baseEnemiesPerWave, maxEnemiesPerWave, difficultyProgress));
    }

    void OnEnemyDied(Enemy deadEnemy)
    {
        // Remove dead enemy from active list
        if (deadEnemy != null && activeEnemies.Contains(deadEnemy.gameObject))
        {
            activeEnemies.Remove(deadEnemy.gameObject);
        }
    }

    void CleanupActiveEnemiesList()
    {
        // Remove null references (destroyed enemies)
        activeEnemies.RemoveAll(enemy => enemy == null);
    }

    // Debug visualization
    void OnDrawGizmosSelected()
    {
        if (playerTransform == null) return;

        // Draw spawn radius
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(playerTransform.position, spawnRadius);

        // Draw minimum spawn distance
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(playerTransform.position, minSpawnDistance);
    }

    // Public methods for external control
    public void SetSpawnRate(float newInterval)
    {
        currentSpawnInterval = Mathf.Max(newInterval, minSpawnInterval);
    }

    public void SetEnemiesPerWave(int newCount)
    {
        currentEnemiesPerWave = Mathf.Clamp(newCount, 1, maxEnemiesPerWave);
    }

    public int GetActiveEnemyCount()
    {
        CleanupActiveEnemiesList();
        return activeEnemies.Count;
    }

    public void ClearAllEnemies()
    {
        foreach (GameObject enemy in activeEnemies)
        {
            if (enemy != null)
            {
                Destroy(enemy);
            }
        }
        activeEnemies.Clear();
    }
}
