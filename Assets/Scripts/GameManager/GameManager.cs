using UnityEngine;
using UnityEngine.SceneManagement;

public class GameManager : MonoBehaviour
{
    [Header("Game Settings")]
    public float gameDurationMinutes = 30f; // Total game duration
    public bool startGameAutomatically = true;
    
    [Header("Player References")]
    public PlayerController player;
    public PlayerLevel playerLevel;
    
    [Header("Spawning")]
    public EnemySpawner enemySpawner;
    
    // Game state
    public enum GameState
    {
        MainMenu,
        Playing,
        Paused,
        GameOver,
        Victory
    }
    
    private GameState currentState = GameState.MainMenu;
    private float gameStartTime;
    private float gameTimer;
    private bool gameEnded = false;

    // Singleton pattern
    public static GameManager Instance { get; private set; }

    // Events for UI and other systems
    public static event System.Action<GameState> OnGameStateChanged;
    public static event System.Action<float> OnGameTimeUpdated; // Passes remaining time in seconds
    public static event System.Action OnGameWon;
    public static event System.Action OnGameLost;

    void Awake()
    {
        // Singleton setup
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
            return;
        }
    }

    void Start()
    {
        // Find required components if not assigned
        if (player == null)
        {
            player = FindObjectOfType<PlayerController>();
        }
        
        if (playerLevel == null && player != null)
        {
            playerLevel = player.GetComponent<PlayerLevel>();
        }
        
        if (enemySpawner == null)
        {
            enemySpawner = FindObjectOfType<EnemySpawner>();
        }

        // Initialize game
        if (startGameAutomatically)
        {
            StartGame();
        }
        else
        {
            SetGameState(GameState.MainMenu);
        }
    }

    void Update()
    {
        if (currentState == GameState.Playing)
        {
            UpdateGameTimer();
            CheckGameEndConditions();
        }

        // Handle input
        HandleInput();
    }

    void HandleInput()
    {
        // Pause/Resume game
        if (Input.GetKeyDown(KeyCode.Escape))
        {
            if (currentState == GameState.Playing)
            {
                PauseGame();
            }
            else if (currentState == GameState.Paused)
            {
                ResumeGame();
            }
        }

        // Restart game
        if (Input.GetKeyDown(KeyCode.R) && (currentState == GameState.GameOver || currentState == GameState.Victory))
        {
            RestartGame();
        }
    }

    void UpdateGameTimer()
    {
        gameTimer += Time.deltaTime;
        float remainingTime = (gameDurationMinutes * 60f) - gameTimer;
        
        // Notify UI of time update
        OnGameTimeUpdated?.Invoke(remainingTime);
        
        // Check if time is up
        if (remainingTime <= 0f && !gameEnded)
        {
            WinGame();
        }
    }

    void CheckGameEndConditions()
    {
        if (gameEnded) return;

        // Check if player is dead
        if (player != null && player.currentHealth <= 0)
        {
            LoseGame();
        }
    }

    public void StartGame()
    {
        Debug.Log("GameManager: Starting new game!");
        
        gameStartTime = Time.time;
        gameTimer = 0f;
        gameEnded = false;
        
        // Reset player if needed
        if (player != null)
        {
            player.currentHealth = player.maxHealth;
            player.enabled = true;
        }
        
        // Reset player level if needed
        if (playerLevel != null)
        {
            // Optionally reset level or keep progression
            // playerLevel.currentLevel = 1;
            // playerLevel.currentExperience = 0;
        }
        
        SetGameState(GameState.Playing);
    }

    public void PauseGame()
    {
        Debug.Log("GameManager: Game paused");
        Time.timeScale = 0f;
        SetGameState(GameState.Paused);
    }

    public void ResumeGame()
    {
        Debug.Log("GameManager: Game resumed");
        Time.timeScale = 1f;
        SetGameState(GameState.Playing);
    }

    public void WinGame()
    {
        if (gameEnded) return;
        
        Debug.Log("GameManager: Player won the game!");
        gameEnded = true;
        
        // Stop enemy spawning
        if (enemySpawner != null)
        {
            enemySpawner.enabled = false;
        }
        
        SetGameState(GameState.Victory);
        OnGameWon?.Invoke();
    }

    public void LoseGame()
    {
        if (gameEnded) return;
        
        Debug.Log("GameManager: Player lost the game!");
        gameEnded = true;
        
        // Disable player
        if (player != null)
        {
            player.enabled = false;
        }
        
        // Stop enemy spawning
        if (enemySpawner != null)
        {
            enemySpawner.enabled = false;
        }
        
        SetGameState(GameState.GameOver);
        OnGameLost?.Invoke();
    }

    public void RestartGame()
    {
        Debug.Log("GameManager: Restarting game");
        
        // Reset time scale
        Time.timeScale = 1f;
        
        // Clear all enemies
        if (enemySpawner != null)
        {
            enemySpawner.ClearAllEnemies();
            enemySpawner.enabled = true;
        }
        
        // Restart the scene
        SceneManager.LoadScene(SceneManager.GetActiveScene().name);
    }

    public void QuitGame()
    {
        Debug.Log("GameManager: Quitting game");
        
        #if UNITY_EDITOR
            UnityEditor.EditorApplication.isPlaying = false;
        #else
            Application.Quit();
        #endif
    }

    void SetGameState(GameState newState)
    {
        if (currentState != newState)
        {
            currentState = newState;
            Debug.Log($"GameManager: Game state changed to {newState}");
            OnGameStateChanged?.Invoke(newState);
        }
    }

    // Public getters
    public GameState GetCurrentState()
    {
        return currentState;
    }

    public float GetGameTime()
    {
        return gameTimer;
    }

    public float GetRemainingTime()
    {
        return (gameDurationMinutes * 60f) - gameTimer;
    }

    public float GetGameProgress()
    {
        return Mathf.Clamp01(gameTimer / (gameDurationMinutes * 60f));
    }

    public bool IsGameActive()
    {
        return currentState == GameState.Playing;
    }

    // Debug info
    void OnGUI()
    {
        if (!Debug.isDebugBuild) return;

        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
        GUILayout.Label($"Game State: {currentState}");
        GUILayout.Label($"Game Time: {gameTimer:F1}s");
        GUILayout.Label($"Remaining: {GetRemainingTime():F1}s");
        
        if (enemySpawner != null)
        {
            GUILayout.Label($"Active Enemies: {enemySpawner.GetActiveEnemyCount()}");
        }
        
        if (player != null)
        {
            GUILayout.Label($"Player Health: {player.currentHealth}/{player.maxHealth}");
        }
        
        if (playerLevel != null)
        {
            GUILayout.Label($"Player Level: {playerLevel.currentLevel}");
            GUILayout.Label($"Player XP: {playerLevel.currentExperience}/{playerLevel.experienceToNextLevel}");
        }
        
        GUILayout.EndArea();
    }
}
