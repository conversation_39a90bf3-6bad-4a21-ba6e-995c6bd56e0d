using UnityEngine;

public class FreshGameSetup : MonoBehaviour
{
    [Header("Fresh Game Setup")]
    public bool setupOnStart = true;
    public bool clearExistingObjects = true;
    
    void Start()
    {
        if (setupOnStart)
        {
            SetupCompleteGame();
        }
    }
    
    [ContextMenu("Setup Complete Game")]
    public void SetupCompleteGame()
    {
        Debug.Log("=== FRESH GAME SETUP STARTING ===");
        
        if (clearExistingObjects)
        {
            ClearExistingGameObjects();
        }
        
        // Step 1: Ensure tags exist
        EnsureTagsExist();
        
        // Step 2: Create player
        CreateFreshPlayer();
        
        // Step 3: Create game manager
        CreateFreshGameManager();
        
        // Step 4: Create simple enemy spawner
        CreateFreshEnemySpawner();
        
        // Step 5: Create some initial enemies for testing
        Invoke("CreateInitialEnemies", 1f);
        
        Debug.Log("=== FRESH GAME SETUP COMPLETE ===");
        Debug.Log("Use WASD to move, enemies should spawn and be attacked automatically!");
    }
    
    void ClearExistingGameObjects()
    {
        Debug.Log("Clearing existing game objects...");
        
        // Clear existing players
        GameObject[] players = GameObject.FindGameObjectsWithTag("Player");
        foreach (GameObject player in players)
        {
            DestroyImmediate(player);
        }
        
        // Clear existing enemies
        GameObject[] enemies = GameObject.FindGameObjectsWithTag("Enemy");
        foreach (GameObject enemy in enemies)
        {
            DestroyImmediate(enemy);
        }
        
        // Clear managers
        GameManager[] gameManagers = FindObjectsOfType<GameManager>();
        foreach (GameManager gm in gameManagers)
        {
            DestroyImmediate(gm.gameObject);
        }
        
        EnemySpawner[] spawners = FindObjectsOfType<EnemySpawner>();
        foreach (EnemySpawner spawner in spawners)
        {
            DestroyImmediate(spawner.gameObject);
        }
        
        SimpleEnemySpawner[] simpleSpawners = FindObjectsOfType<SimpleEnemySpawner>();
        foreach (SimpleEnemySpawner spawner in simpleSpawners)
        {
            DestroyImmediate(spawner.gameObject);
        }
    }
    
    void EnsureTagsExist()
    {
        Debug.Log("Ensuring tags exist...");
        // Note: In runtime, we can't create tags, but we can check if they exist
        // Tags need to be created manually in Project Settings
    }
    
    void CreateFreshPlayer()
    {
        Debug.Log("Creating fresh player...");
        
        GameObject player = new GameObject("Player");
        player.tag = "Player";
        player.transform.position = Vector3.zero;
        
        // Add PlayerController
        PlayerController pc = player.AddComponent<PlayerController>();
        pc.moveSpeed = 5f;
        pc.maxHealth = 100;
        pc.currentHealth = 100;
        
        // Add PlayerLevel
        PlayerLevel pl = player.AddComponent<PlayerLevel>();
        pl.currentLevel = 1;
        pl.currentExperience = 0;
        pl.experienceToNextLevel = 100;
        
        // Add physics
        Rigidbody2D rb = player.AddComponent<Rigidbody2D>();
        rb.gravityScale = 0f;
        rb.freezeRotation = true;
        
        // Add collider
        CircleCollider2D col = player.AddComponent<CircleCollider2D>();
        col.radius = 0.5f;
        
        // Add visual
        SpriteRenderer sr = player.AddComponent<SpriteRenderer>();
        sr.sprite = CreateCircleSprite(Color.blue, 64);
        sr.sortingOrder = 2;
        
        // Create weapon
        GameObject weaponObj = new GameObject("MagicWand");
        weaponObj.transform.SetParent(player.transform);
        
        MagicWand weapon = weaponObj.AddComponent<MagicWand>();
        weapon.baseDamage = 25f;
        weapon.baseCooldown = 0.8f;
        weapon.projectileSpeed = 15f;
        weapon.findTargetRange = 15f;
        
        Debug.Log("Player created successfully!");
    }
    
    void CreateFreshGameManager()
    {
        Debug.Log("Creating fresh game manager...");
        
        GameObject gm = new GameObject("GameManager");
        GameManager gmScript = gm.AddComponent<GameManager>();
        gmScript.gameDurationMinutes = 30f;
        gmScript.startGameAutomatically = true;
        
        Debug.Log("GameManager created!");
    }
    
    void CreateFreshEnemySpawner()
    {
        Debug.Log("Creating fresh enemy spawner...");
        
        GameObject spawnerObj = new GameObject("SimpleEnemySpawner");
        SimpleEnemySpawner spawner = spawnerObj.AddComponent<SimpleEnemySpawner>();
        
        // Find and assign player
        GameObject player = GameObject.FindGameObjectWithTag("Player");
        if (player != null)
        {
            spawner.playerTransform = player.transform;
        }
        
        spawner.spawnRadius = 15f;
        spawner.minSpawnDistance = 8f;
        spawner.spawnInterval = 3f;
        spawner.maxEnemies = 20;
        
        Debug.Log("SimpleEnemySpawner created!");
    }
    
    void CreateInitialEnemies()
    {
        Debug.Log("Creating initial test enemies...");
        
        GameObject player = GameObject.FindGameObjectWithTag("Player");
        if (player == null)
        {
            Debug.LogError("No player found for initial enemies!");
            return;
        }
        
        // Create 3 test enemies
        for (int i = 0; i < 3; i++)
        {
            float angle = i * 120f * Mathf.Deg2Rad;
            Vector3 spawnPos = player.transform.position + new Vector3(
                Mathf.Cos(angle) * 6f,
                Mathf.Sin(angle) * 6f,
                0f
            );
            
            CreateEnemyAt(spawnPos, $"InitialEnemy_{i}");
        }
        
        Debug.Log("Initial enemies created!");
    }
    
    GameObject CreateEnemyAt(Vector3 position, string name)
    {
        GameObject enemy = new GameObject(name);
        enemy.transform.position = position;
        enemy.tag = "Enemy";
        
        // Add BasicEnemy script
        BasicEnemy enemyScript = enemy.AddComponent<BasicEnemy>();
        
        // Add physics
        Rigidbody2D rb = enemy.AddComponent<Rigidbody2D>();
        rb.gravityScale = 0f;
        rb.freezeRotation = true;
        
        // Add collider
        CircleCollider2D col = enemy.AddComponent<CircleCollider2D>();
        col.radius = 0.4f;
        
        // Add visual
        SpriteRenderer sr = enemy.AddComponent<SpriteRenderer>();
        sr.sprite = CreateCircleSprite(Color.red, 48);
        sr.sortingOrder = 1;
        
        return enemy;
    }
    
    Sprite CreateCircleSprite(Color color, int size)
    {
        Texture2D texture = new Texture2D(size, size);
        Color[] pixels = new Color[size * size];
        
        Vector2 center = new Vector2(size / 2f, size / 2f);
        float radius = size / 2f - 2f;
        
        for (int x = 0; x < size; x++)
        {
            for (int y = 0; y < size; y++)
            {
                float distance = Vector2.Distance(new Vector2(x, y), center);
                if (distance <= radius)
                {
                    pixels[y * size + x] = color;
                }
                else
                {
                    pixels[y * size + x] = Color.clear;
                }
            }
        }
        
        texture.SetPixels(pixels);
        texture.Apply();
        
        return Sprite.Create(texture, new Rect(0, 0, size, size), new Vector2(0.5f, 0.5f));
    }
    
    void Update()
    {
        // Debug key
        if (Input.GetKeyDown(KeyCode.F5))
        {
            SetupCompleteGame();
        }
    }
    
    void OnGUI()
    {
        GUILayout.BeginArea(new Rect(10, Screen.height - 60, 300, 50));
        GUILayout.Label("F5 - Fresh setup");
        if (GUILayout.Button("Fresh Setup Now"))
        {
            SetupCompleteGame();
        }
        GUILayout.EndArea();
    }
}
