using UnityEngine;

public class ComponentBasedTargeting : MonoBehaviour
{
    private MagicWand weapon;
    
    void Start()
    {
        weapon = GetComponent<MagicWand>();
        if (weapon == null)
        {
            Debug.LogError("ComponentBasedTargeting requires MagicWand component!");
            enabled = false;
        }
    }
    
    void Update()
    {
        // Override the weapon's targeting system
        if (weapon != null)
        {
            // This will be called by the weapon when it needs to find targets
        }
    }
    
    public Transform FindClosestEnemyByComponent(float range, Vector3 originPosition)
    {
        // Find all objects with Enemy components instead of using tags
        Enemy[] enemies = FindObjectsOfType<Enemy>();
        BasicEnemy[] basicEnemies = FindObjectsOfType<BasicEnemy>();
        EnemyMarker[] markedEnemies = FindObjectsOfType<EnemyMarker>();
        
        Transform closestTarget = null;
        float shortestDistance = range;
        
        // Check Enemy components
        foreach (Enemy enemy in enemies)
        {
            if (!enemy.gameObject.activeInHierarchy) continue;
            
            float distance = Vector2.Distance(originPosition, enemy.transform.position);
            if (distance < shortestDistance)
            {
                closestTarget = enemy.transform;
                shortestDistance = distance;
            }
        }
        
        // Check BasicEnemy components
        foreach (BasicEnemy enemy in basicEnemies)
        {
            if (!enemy.gameObject.activeInHierarchy) continue;
            
            float distance = Vector2.Distance(originPosition, enemy.transform.position);
            if (distance < shortestDistance)
            {
                closestTarget = enemy.transform;
                shortestDistance = distance;
            }
        }
        
        // Check EnemyMarker components (fallback when tags don't work)
        foreach (EnemyMarker enemy in markedEnemies)
        {
            if (!enemy.gameObject.activeInHierarchy) continue;
            
            float distance = Vector2.Distance(originPosition, enemy.transform.position);
            if (distance < shortestDistance)
            {
                closestTarget = enemy.transform;
                shortestDistance = distance;
            }
        }
        
        if (closestTarget != null)
        {
            Debug.Log($"ComponentBasedTargeting found target: {closestTarget.name} at distance {shortestDistance}");
        }
        
        return closestTarget;
    }
}
