using UnityEngine;

public class ProjectileDebugger : MonoBehaviour
{
    [Header("Projectile Debug")]
    public bool showDebugInfo = true;
    public bool logProjectileCreation = true;
    
    void Start()
    {
        Debug.Log("ProjectileDebugger: Started monitoring projectiles");
    }
    
    void Update()
    {
        if (Input.GetKeyDown(KeyCode.F8))
        {
            DebugProjectileSystem();
        }
        
        if (Input.GetKeyDown(KeyCode.F9))
        {
            ForceCreateTestProjectile();
        }
        
        if (logProjectileCreation)
        {
            // Monitor for new projectiles
            Projectile[] projectiles = FindObjectsOfType<Projectile>();
            if (projectiles.Length > 0)
            {
                foreach (Projectile proj in projectiles)
                {
                    if (proj.gameObject.name.Contains("Test") == false)
                    {
                        Debug.Log($"Active projectile found: {proj.name} at {proj.transform.position}");
                    }
                }
            }
        }
    }
    
    void DebugProjectileSystem()
    {
        Debug.Log("=== PROJECTILE SYSTEM DEBUG ===");
        
        // Check for player and weapon
        PlayerController player = FindObjectOfType<PlayerController>();
        if (player == null)
        {
            Debug.LogError("No PlayerController found!");
            return;
        }
        
        MagicWand weapon = player.GetComponentInChildren<MagicWand>();
        if (weapon == null)
        {
            Debug.LogError("No MagicWand found on player!");
            return;
        }
        
        Debug.Log($"Player found: {player.name}");
        Debug.Log($"Weapon found: {weapon.name}");
        Debug.Log($"Weapon projectile prefab: {(weapon.projectilePrefab != null ? "✓" : "✗")}");
        Debug.Log($"Weapon damage: {weapon.CurrentDamage}");
        Debug.Log($"Weapon cooldown: {weapon.CurrentCooldown}");
        Debug.Log($"Weapon range: {weapon.findTargetRange}");
        
        // Check for enemies
        BasicEnemy[] enemies = FindObjectsOfType<BasicEnemy>();
        Debug.Log($"BasicEnemies found: {enemies.Length}");
        
        if (enemies.Length > 0)
        {
            BasicEnemy closestEnemy = null;
            float closestDistance = float.MaxValue;
            
            foreach (BasicEnemy enemy in enemies)
            {
                float distance = Vector2.Distance(player.transform.position, enemy.transform.position);
                Debug.Log($"Enemy {enemy.name} at distance: {distance}");
                
                if (distance < closestDistance)
                {
                    closestDistance = distance;
                    closestEnemy = enemy;
                }
            }
            
            if (closestEnemy != null)
            {
                Debug.Log($"Closest enemy: {closestEnemy.name} at distance {closestDistance}");
                Debug.Log($"Within weapon range? {(closestDistance <= weapon.findTargetRange ? "YES" : "NO")}");
            }
        }
        
        // Check for existing projectiles
        Projectile[] projectiles = FindObjectsOfType<Projectile>();
        Debug.Log($"Active projectiles: {projectiles.Length}");
        
        foreach (Projectile proj in projectiles)
        {
            Debug.Log($"Projectile: {proj.name} at {proj.transform.position}, damage: {proj.damage}");
        }
        
        Debug.Log("=== END PROJECTILE DEBUG ===");
    }
    
    void ForceCreateTestProjectile()
    {
        Debug.Log("Creating test projectile...");
        
        PlayerController player = FindObjectOfType<PlayerController>();
        if (player == null)
        {
            Debug.LogError("No player found for test projectile!");
            return;
        }
        
        BasicEnemy[] enemies = FindObjectsOfType<BasicEnemy>();
        if (enemies.Length == 0)
        {
            Debug.LogError("No enemies found for test projectile!");
            return;
        }
        
        // Create a test projectile manually
        GameObject testProjectile = new GameObject("TestProjectile");
        testProjectile.transform.position = player.transform.position;
        
        // Add Projectile component
        Projectile proj = testProjectile.AddComponent<Projectile>();
        proj.speed = 15f;
        proj.damage = 25f;
        proj.lifetime = 3f;
        proj.pierceCount = 0;
        
        // Add physics
        Rigidbody2D rb = testProjectile.AddComponent<Rigidbody2D>();
        rb.gravityScale = 0f;
        
        // Add collider
        CircleCollider2D col = testProjectile.AddComponent<CircleCollider2D>();
        col.radius = 0.1f;
        col.isTrigger = true;
        
        // Add visual
        SpriteRenderer sr = testProjectile.AddComponent<SpriteRenderer>();
        sr.sprite = CreateProjectileSprite();
        sr.sortingOrder = 3;
        
        // Set direction towards closest enemy
        BasicEnemy target = enemies[0];
        Vector2 direction = (target.transform.position - player.transform.position).normalized;
        
        // Initialize projectile
        proj.Initialize(direction, 25f, 0, 15f, 3f);
        
        Debug.Log($"Test projectile created, targeting {target.name}");
    }
    
    Sprite CreateProjectileSprite()
    {
        int size = 16;
        Texture2D texture = new Texture2D(size, size);
        Color[] pixels = new Color[size * size];
        
        Vector2 center = new Vector2(size / 2f, size / 2f);
        float radius = size / 2f - 1f;
        
        for (int x = 0; x < size; x++)
        {
            for (int y = 0; y < size; y++)
            {
                float distance = Vector2.Distance(new Vector2(x, y), center);
                if (distance <= radius)
                {
                    pixels[y * size + x] = Color.yellow;
                }
                else
                {
                    pixels[y * size + x] = Color.clear;
                }
            }
        }
        
        texture.SetPixels(pixels);
        texture.Apply();
        
        return Sprite.Create(texture, new Rect(0, 0, size, size), new Vector2(0.5f, 0.5f));
    }
    
    void OnGUI()
    {
        if (!showDebugInfo) return;
        
        GUILayout.BeginArea(new Rect(Screen.width - 250, Screen.height - 120, 240, 110));
        GUILayout.Label("=== PROJECTILE DEBUG ===");
        GUILayout.Label("F8 - Debug projectile system");
        GUILayout.Label("F9 - Force create test projectile");
        
        if (GUILayout.Button("Debug Projectiles"))
        {
            DebugProjectileSystem();
        }
        
        if (GUILayout.Button("Create Test Projectile"))
        {
            ForceCreateTestProjectile();
        }
        
        GUILayout.EndArea();
    }
}
