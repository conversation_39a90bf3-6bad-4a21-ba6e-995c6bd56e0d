using UnityEngine;

public class MagicWand : WeaponBase
{
    [Header("Magic Wand Specifics")]
    public GameObject projectilePrefab; // Assign your projectile prefab in the Inspector
    public float projectileSpeed = 15f;
    public int baseProjectilePierceCount = 0; // Default: no pierce for level 1 wand
    public float projectileLifetime = 3f;
    public float findTargetRange = 15f; // Range to find the closest enemy

    // Weapon specific stats that might be upgraded
    private int numberOfProjectiles = 1; // Base number of projectiles per attack
    private int currentPierceCount; // Actual pierce count, can be upgraded

    protected override void Start()
    {
        base.Start(); // Call the base Start method
        weaponName = "Magic Wand"; // Set the weapon name
        currentPierceCount = baseProjectilePierceCount;

        if (projectilePrefab == null)
        {
            Debug.LogError("MagicWand on " + gameObject.name + ": Projectile Prefab is not assigned in the Inspector!", this);
        }
    }

    protected override void Attack()
    {
        if (projectilePrefab == null) 
        {
            // Warning already given in Start, but good to prevent null refs
            return; 
        }

        if (owner == null)
        {
            // Warning already given in base.Start if owner not found
            return;
        }
        
        // Find the closest enemy to the player (owner)
        Transform targetEnemy = FindClosestEnemy(findTargetRange, owner.transform.position);

        if (targetEnemy != null)
        {
            for (int i = 0; i < numberOfProjectiles; i++)
            {
                GameObject projGO = Instantiate(projectilePrefab, owner.transform.position, Quaternion.identity);
                Projectile projectileScript = projGO.GetComponent<Projectile>();

                if (projectileScript != null)
                {
                    Vector2 directionToTarget = (targetEnemy.position - owner.transform.position).normalized;
                    
                    // Optional: Implement spread for multiple projectiles later if needed
                    // if (numberOfProjectiles > 1) { ... }

                    projectileScript.Initialize(
                        directionToTarget,
                        CurrentDamage, // Uses CurrentDamage from WeaponBase, which includes modifiers
                        currentPierceCount, // Use the potentially upgraded pierce count
                        projectileSpeed,
                        projectileLifetime
                        // owner // Pass owner if Projectile script needs it for any reason
                    );
                }
                else
                {
                    Debug.LogError("MagicWand: Instantiated projectile does not have a Projectile component! Destroying it.", projGO);
                    Destroy(projGO);
                }
            }
            // Debug.Log($"{weaponName} attacked, firing {numberOfProjectiles} projectile(s) at {targetEnemy.name}.");
        }
        // else
        // {
        //     Debug.Log($"{weaponName}: No enemy in range ({findTargetRange} units) to attack.");
        // }
    }

    public override void LevelUpWeapon()
    {
        base.LevelUpWeapon(); // Handles level increment and base log

        // Apply Magic Wand specific upgrades based on the new 'level' (already incremented by base call)
        switch (level)
        {
            case 2:
                baseDamage *= 1.25f; // Increase damage by 25%
                Debug.Log($"{weaponName} Lvl {level}: Damage increased to {CurrentDamage}.");
                break;
            case 3:
                baseCooldown *= 0.85f; // Reduce cooldown by 15%
                Debug.Log($"{weaponName} Lvl {level}: Cooldown reduced to {CurrentCooldown}.");
                break;
            case 4:
                numberOfProjectiles = 2;
                Debug.Log($"{weaponName} Lvl {level}: Now fires {numberOfProjectiles} projectiles.");
                break;
            case 5:
                currentPierceCount = 1; // Projectiles now pierce one enemy
                Debug.Log($"{weaponName} Lvl {level}: Projectiles now pierce {currentPierceCount} enemy.");
                break;
            case 6:
                baseDamage *= 1.25f; // Another damage increase
                 numberOfProjectiles = 3;
                Debug.Log($"{weaponName} Lvl {level}: Damage further increased and fires {numberOfProjectiles} projectiles.");
                break;
            // Add more levels as desired
            default:
                // Generic small bonus for very high levels if not specified
                if (level > 6) baseDamage *= 1.05f;
                break;
        }
    }

    // If the Magic Wand should only attempt an attack (and thus reset its cooldown) 
    // if an enemy is actually in range, uncomment and use this.
    // Otherwise, it will cooldown even if it didn't find a target to shoot at.
    /*
    protected override bool CanAttack()
    {
        if (owner == null || projectilePrefab == null) return false;
        return FindClosestEnemy(findTargetRange, owner.transform.position) != null;
    }
    */
}