# 吸血鬼幸存者类游戏 - 设置指南

## 第一阶段完成！核心系统已实现

我们已经完成了游戏的核心战斗循环系统。以下是设置步骤：

## 1. 标签设置 (Tags)
在Unity中设置以下标签：
- `Player` - 玩家对象
- `Enemy` - 敌人对象
- `Obstacle` - 障碍物（可选）

**设置方法：**
1. 打开 Edit → Project Settings → Tags and Layers
2. 在 Tags 部分添加上述标签

## 2. 玩家设置 (Player Setup)

### 创建玩家对象：
1. 创建一个空的GameObject，命名为 "Player"
2. 设置标签为 "Player"
3. 添加以下组件：
   - `PlayerController` 脚本
   - `PlayerLevel` 脚本
   - `Rigidbody2D` (Gravity Scale = 0)
   - `Collider2D` (Circle Collider 推荐)
   - `SpriteRenderer` (添加玩家精灵)

### 添加武器：
1. 在Player对象下创建子对象 "MagicWand"
2. 添加 `MagicWand` 脚本
3. 在Inspector中设置：
   - Projectile Prefab (需要创建弹射物预制体)
   - 其他武器参数

## 3. 弹射物预制体 (Projectile Prefab)

### 创建弹射物预制体：
1. 创建GameObject，命名为 "MagicProjectile"
2. 添加组件：
   - `Projectile` 脚本
   - `Rigidbody2D` (Gravity Scale = 0)
   - `Collider2D` (Is Trigger = true)
   - `SpriteRenderer` (添加弹射物精灵)
3. 保存为预制体到 Assets/Prefabs/ 文件夹

## 4. 敌人预制体 (Enemy Prefab)

### 创建基础敌人预制体：
1. 创建GameObject，命名为 "BasicEnemy"
2. 设置标签为 "Enemy"
3. 添加组件：
   - `BasicEnemy` 脚本
   - `Rigidbody2D` (Gravity Scale = 0, Freeze Rotation Z = true)
   - `Collider2D` (用于碰撞检测)
   - `SpriteRenderer` (添加敌人精灵)
4. 保存为预制体

## 5. 经验宝石预制体 (Experience Gem Prefab)

### 创建经验宝石预制体：
1. 创建GameObject，命名为 "ExperienceGem"
2. 添加组件：
   - `ExperienceGem` 脚本
   - `Collider2D` (Is Trigger = true)
   - `SpriteRenderer` (添加宝石精灵)
3. 保存为预制体到 Resources/Prefabs/ 文件夹
   - **重要：** 必须放在 Resources/Prefabs/ 路径下，因为敌人死亡时会通过 Resources.Load 加载

## 6. 游戏管理器设置 (Game Manager Setup)

### 创建游戏管理器：
1. 创建空的GameObject，命名为 "GameManager"
2. 添加 `GameManager` 脚本
3. 在Inspector中设置：
   - Game Duration Minutes (默认30分钟)
   - Player 引用
   - Enemy Spawner 引用

## 7. 敌人生成器设置 (Enemy Spawner Setup)

### 创建敌人生成器：
1. 创建空的GameObject，命名为 "EnemySpawner"
2. 添加 `EnemySpawner` 脚本
3. 在Inspector中设置：
   - Enemy Prefabs 数组（拖入敌人预制体）
   - Player Transform 引用
   - 生成参数（半径、间隔等）

## 8. 场景设置建议

### 摄像机设置：
- 添加一个跟随玩家的摄像机脚本，或者将摄像机设为Player的子对象

### 地图设置：
- 创建一个大的背景精灵或平铺地面
- 可以添加一些装饰性的障碍物

## 9. 测试步骤

1. **基础移动测试：**
   - 运行游戏，使用WASD或方向键移动玩家
   - 确认玩家移动正常

2. **武器测试：**
   - 确保场景中有敌人
   - 观察魔法棒是否自动攻击最近的敌人
   - 检查弹射物是否正确命中敌人并造成伤害

3. **敌人测试：**
   - 观察敌人是否正确生成
   - 确认敌人会追踪玩家
   - 测试敌人死亡后是否掉落经验宝石

4. **经验系统测试：**
   - 击杀敌人后检查是否获得经验
   - 确认升级时有相应的日志输出

## 10. 常见问题解决

### 如果武器不攻击：
- 检查敌人是否有 "Enemy" 标签
- 确认弹射物预制体已正确分配给MagicWand
- 检查FindTargetRange是否足够大

### 如果敌人不受伤害：
- 确认弹射物的Collider2D设置为 Is Trigger = true
- 检查敌人是否有Enemy脚本组件

### 如果经验宝石不掉落：
- 确保经验宝石预制体在 Resources/Prefabs/ExperienceGem 路径下
- 检查敌人的experienceValue是否大于0

## 下一步

完成基础设置后，我们可以继续实现：
- UI系统（血量条、经验条）
- 升级选择界面
- 更多武器类型
- 被动道具系统

如果遇到任何问题，请告诉我具体的错误信息！
