using UnityEngine;

public class ExperienceGem : MonoBehaviour
{
    public int experienceValue = 10; // Amount of XP this gem gives
    public float attractionSpeed = 5f; // Speed at which gem flies towards player when in range
    public float attractionRadius = 3f; // Radius within which the gem starts moving towards the player

    private Transform playerTransform;
    private bool isAttracted = false;

    void Start()
    {
        // Try to find the player GameObject by tag. Make sure your player has the "Player" tag.
        GameObject playerObject = GameObject.FindGameObjectWithTag("Player");
        if (playerObject != null)
        {
            playerTransform = playerObject.transform;
        }
        else
        {
            Debug.LogWarning("ExperienceGem: Player not found. Make sure the player GameObject has the 'Player' tag.");
        }

        // Ensure the gem has a trigger collider to detect the player
        Collider2D col = GetComponent<Collider2D>();
        if (col == null)
        {
            Debug.LogError("ExperienceGem on " + gameObject.name + " requires a Collider2D component.", this);
        }
        else if (!col.isTrigger)
        {
            // For pickup detection by OnTriggerEnter2D, the collider needs to be a trigger.
            // You can set this in the Unity Editor on the Collider2D component.
            Debug.LogWarning("ExperienceGem's Collider2D on " + gameObject.name + " should be set to 'Is Trigger = true' for pickup detection via OnTriggerEnter2D.", this);
        }
    }

    void Update()
    {
        if (playerTransform == null) return; // Don't do anything if player isn't found

        // Calculate distance to player
        float distanceToPlayer = Vector2.Distance(transform.position, playerTransform.position);

        // Check if player is within attraction radius
        if (distanceToPlayer <= attractionRadius)
        {
            isAttracted = true;
        }

        // If attracted, move towards the player
        if (isAttracted)
        {
            transform.position = Vector2.MoveTowards(transform.position, playerTransform.position, attractionSpeed * Time.deltaTime);
        }
    }

    // This function is called when another Collider2D enters this object's trigger Collider2D
    void OnTriggerEnter2D(Collider2D other)
    {
        // Check if the object we collided with is the player (by tag)
        if (other.CompareTag("Player"))
        {
            // Try to get the PlayerLevel component from the player object
            PlayerLevel playerLevel = other.GetComponent<PlayerLevel>();
            if (playerLevel != null)
            {
                playerLevel.AddExperience(experienceValue); // Add experience to the player
                // TODO: Instantiate a pickup visual effect or play a sound effect here
                Destroy(gameObject); // Destroy the experience gem object
            }
            else
            {
                // This warning helps if PlayerLevel script is missing on the Player object
                Debug.LogWarning("ExperienceGem: Collided with Player, but PlayerLevel component not found on Player object.");
            }
        }
    }

    // Optional: This will draw a yellow wire sphere in the Scene view 
    // to visualize the attractionRadius when the gem is selected.
    void OnDrawGizmosSelected()
    {
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, attractionRadius);
    }
}