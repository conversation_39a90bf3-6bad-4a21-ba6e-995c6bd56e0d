using UnityEngine;

public class TagFixer : MonoBehaviour
{
    [Header("Tag Diagnosis and Fix")]
    public bool fixTagsOnStart = true;
    public bool useComponentDetectionInsteadOfTags = true;
    
    void Start()
    {
        if (fixTagsOnStart)
        {
            DiagnoseAndFixTags();
        }
    }
    
    [ContextMenu("Diagnose Tags")]
    public void DiagnoseAndFixTags()
    {
        Debug.Log("=== TAG DIAGNOSIS ===");
        
        // Check all GameObjects in scene
        GameObject[] allObjects = FindObjectsOfType<GameObject>();
        
        foreach (GameObject obj in allObjects)
        {
            // Check for player components
            if (obj.GetComponent<PlayerController>() != null)
            {
                Debug.Log($"Found PlayerController on: {obj.name}, current tag: '{obj.tag}'");
                if (obj.tag != "Player")
                {
                    Debug.Log($"Fixing player tag on {obj.name}");
                    try
                    {
                        obj.tag = "Player";
                        Debug.Log($"Player tag set successfully on {obj.name}");
                    }
                    catch (System.Exception e)
                    {
                        Debug.LogError($"Failed to set Player tag: {e.Message}");
                        Debug.LogError("Please manually add 'Player' tag in Project Settings > Tags and Layers");
                    }
                }
            }
            
            // Check for enemy components
            if (obj.GetComponent<BasicEnemy>() != null || obj.GetComponent<Enemy>() != null)
            {
                Debug.Log($"Found Enemy component on: {obj.name}, current tag: '{obj.tag}'");
                if (obj.tag != "Enemy")
                {
                    Debug.Log($"Fixing enemy tag on {obj.name}");
                    try
                    {
                        obj.tag = "Enemy";
                        Debug.Log($"Enemy tag set successfully on {obj.name}");
                    }
                    catch (System.Exception e)
                    {
                        Debug.LogError($"Failed to set Enemy tag: {e.Message}");
                        Debug.LogError("Please manually add 'Enemy' tag in Project Settings > Tags and Layers");
                    }
                }
            }
        }
        
        Debug.Log("=== TAG DIAGNOSIS COMPLETE ===");
        
        if (useComponentDetectionInsteadOfTags)
        {
            Debug.Log("Switching to component-based detection...");
            UpdateWeaponToUseComponents();
        }
    }
    
    void UpdateWeaponToUseComponents()
    {
        // Find all weapons and update them to use component detection
        MagicWand[] weapons = FindObjectsOfType<MagicWand>();
        
        foreach (MagicWand weapon in weapons)
        {
            // Add a component-based enemy finder
            if (weapon.GetComponent<ComponentBasedTargeting>() == null)
            {
                weapon.gameObject.AddComponent<ComponentBasedTargeting>();
                Debug.Log($"Added ComponentBasedTargeting to {weapon.name}");
            }
        }
    }
    
    void Update()
    {
        if (Input.GetKeyDown(KeyCode.F4))
        {
            DiagnoseAndFixTags();
        }
        
        if (Input.GetKeyDown(KeyCode.F6))
        {
            ForceCreateTaggedObjects();
        }
    }
    
    void ForceCreateTaggedObjects()
    {
        Debug.Log("Force creating properly tagged objects...");
        
        // Create a test enemy with proper tagging
        GameObject testEnemy = new GameObject("ForceTaggedEnemy");
        testEnemy.transform.position = Vector3.right * 5f;
        
        // Add components first
        BasicEnemy enemyScript = testEnemy.AddComponent<BasicEnemy>();
        Rigidbody2D rb = testEnemy.AddComponent<Rigidbody2D>();
        rb.gravityScale = 0f;
        rb.freezeRotation = true;
        
        CircleCollider2D col = testEnemy.AddComponent<CircleCollider2D>();
        col.radius = 0.4f;
        
        SpriteRenderer sr = testEnemy.AddComponent<SpriteRenderer>();
        sr.sprite = CreateSimpleSprite(Color.red);
        
        // Try to set tag
        try
        {
            testEnemy.tag = "Enemy";
            Debug.Log($"Successfully tagged enemy: {testEnemy.tag}");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to tag enemy: {e.Message}");
            // Add a marker component instead
            testEnemy.AddComponent<EnemyMarker>();
            Debug.Log("Added EnemyMarker component instead of tag");
        }
    }
    
    Sprite CreateSimpleSprite(Color color)
    {
        Texture2D texture = new Texture2D(32, 32);
        Color[] pixels = new Color[32 * 32];
        for (int i = 0; i < pixels.Length; i++)
        {
            pixels[i] = color;
        }
        texture.SetPixels(pixels);
        texture.Apply();
        
        return Sprite.Create(texture, new Rect(0, 0, 32, 32), new Vector2(0.5f, 0.5f));
    }
    
    void OnGUI()
    {
        GUILayout.BeginArea(new Rect(Screen.width - 250, 10, 240, 120));
        GUILayout.Label("=== TAG FIXER ===");
        GUILayout.Label("F4 - Diagnose & Fix Tags");
        GUILayout.Label("F6 - Force Create Tagged Enemy");
        
        if (GUILayout.Button("Fix Tags Now"))
        {
            DiagnoseAndFixTags();
        }
        
        if (GUILayout.Button("Create Test Enemy"))
        {
            ForceCreateTaggedObjects();
        }
        
        GUILayout.EndArea();
    }
}

// Marker component for enemies when tags don't work
public class EnemyMarker : MonoBehaviour
{
    // This component marks an object as an enemy
    // when the tag system doesn't work properly
}
