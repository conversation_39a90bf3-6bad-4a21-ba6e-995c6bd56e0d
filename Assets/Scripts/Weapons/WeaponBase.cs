using UnityEngine;

public abstract class WeaponBase : MonoBehaviour
{
    [Header("Weapon Stats")]
    public string weaponName = "Default Weapon";
    public int level = 1;
    public float baseDamage = 10f;
    public float baseCooldown = 1f; // Time between attacks
    
    // Properties that can be modified by upgrades or passives
    public virtual float CurrentDamage => baseDamage * GetDamageModifier();
    public virtual float CurrentCooldown => baseCooldown * GetCooldownModifier();

    protected float currentCooldownTimer = 0f;
    protected PlayerController owner; // Reference to the player who owns this weapon
    protected PlayerLevel ownerLevel; // Reference to player's level component

    protected virtual void Start()
    {
        owner = GetComponentInParent<PlayerController>();
        if (owner == null) owner = GetComponent<PlayerController>(); // Fallback if not parented
        if (owner != null) ownerLevel = owner.GetComponent<PlayerLevel>();

        if (owner == null)
        {
            Debug.LogWarning($"Weapon '{weaponName}' on {gameObject.name} could not find a PlayerController owner.");
        }
        if (ownerLevel == null && owner != null)
        {
            Debug.LogWarning($"Weapon '{weaponName}' on {gameObject.name} could not find PlayerLevel on owner {owner.name}.");
        }
        currentCooldownTimer = 0f; // Start ready to fire for most weapons
    }

    protected virtual void Update()
    {
        if (owner == null || !owner.enabled) // Don't attack if owner is dead/disabled
        {
            return;
        }

        if (currentCooldownTimer > 0)
        {
            currentCooldownTimer -= Time.deltaTime;
        }
        else
        {
            if (CanAttack())
            {
                Attack();
                ResetCooldown();
            }
        }
    }

    protected abstract void Attack(); // To be implemented by each specific weapon type

    protected virtual bool CanAttack()
    {
        return true; // Base condition, can be overridden (e.g., if no enemies are in range)
    }

    protected virtual void ResetCooldown()
    {
        currentCooldownTimer = CurrentCooldown;
    }

    public virtual void LevelUpWeapon()
    {
        level++;
        Debug.Log($"{weaponName} leveled up to {level}!");
        // Example: Each weapon type can override this to apply specific stat boosts
        // baseDamage *= 1.1f; 
        // baseCooldown *= 0.95f;
    }

    // Modifiers can be influenced by player's global stats or passive items
    protected virtual float GetDamageModifier()
    {
        float modifier = 1f;
        // Example: if (ownerLevel != null) modifier += ownerLevel.currentLevel * 0.05f; // +5% damage per player level
        return modifier;
    }

    protected virtual float GetCooldownModifier()
    {
        float modifier = 1f;
        // Example: if (SomePassiveEffectIsActive) modifier *= 0.8f; // 20% cooldown reduction
        return modifier;
    }

    // Helper method to find the closest enemy within a given range
    protected Transform FindClosestEnemy(float searchRange, Vector3 searchPosition)
    {
        GameObject[] enemies = GameObject.FindGameObjectsWithTag("Enemy");
        Transform closestEnemy = null;
        float closestDistanceSqr = searchRange * searchRange;

        foreach (GameObject enemyGO in enemies)
        {
            if (!enemyGO.activeInHierarchy) continue;

            Vector3 directionToEnemy = enemyGO.transform.position - searchPosition;
            float distanceSqr = directionToEnemy.sqrMagnitude;

            if (distanceSqr < closestDistanceSqr)
            {
                closestDistanceSqr = distanceSqr;
                closestEnemy = enemyGO.transform;
            }
        }

        return closestEnemy;
    }

    // Helper to find the closest enemy. Can be used by weapons that target.
    protected Transform FindClosestEnemy(float range, Vector3 originPosition)
    {
        GameObject[] enemies = GameObject.FindGameObjectsWithTag("Enemy"); // Assumes enemies are tagged "Enemy"
        Transform closestTarget = null;
        float shortestDistance = range;

        foreach (GameObject enemyObject in enemies)
        {
            if (!enemyObject.activeInHierarchy) continue;

            float distanceToEnemy = Vector2.Distance(originPosition, enemyObject.transform.position);
            if (distanceToEnemy < shortestDistance)
            {
                closestTarget = enemyObject.transform;
                shortestDistance = distanceToEnemy;
            }
        }
        return closestTarget;
    }
}